# This Yaml Document has been converted by ESAI Yaml Pipeline Conversion Tool.
# Please make sure to check all the converted content, it is your team's responsibility to make sure that the pipeline is still valid and functions as expected.
# This pipeline will be extended to the WebXT template
# The pool section has been updated for Kubernetes tasks with Ubuntu image
# Artifact paths have been adjusted from $(Pipeline.Workspace) to $(System.ArtifactsDirectory)
# Build.BuildId references have been replaced with resources.pipeline.CampaignMTTest.runID

trigger: none
name: $(Date:yyyyMMdd).$(Rev:r)
variables:
- name: allTestNames
  value: DB_BVT,DB_Regression,DM_BVT,FDP_BVT,MT_BVT,MT_Regression,SA_BVT,MT_Reparenting
- name: Area
  value: MT
- name: Environment
  value: SI
- name: ImageTag
  value: ''
- name: OVERRIDE_REPOVERSION_ADSAPPS_CLOUDTEST
  value: master
- name: OVERRIDE_REPOVERSION_ADSAPPSMT
  value: master
- name: PatAuthToken
  value: 
- name: RunId
  value: ''
- name: RunType
  value: VSTSPHX
- name: SITestVariableGroupId
  value: 526
- name: system.TeamFoundationServerUri
  value: https://msasg.vsrm.visualstudio.com
- name: TestContainers
  value: CampaignManagementMT\ApiFunctionalTests.V12.dll;CampaignManagementMT\ApiFunctionalTests.V13.dll;CampaignManagementMT\Microsoft.Advertising.Advertiser.Api.V2.dll;CampaignManagementMT\FunctionalTests.dll;MTIntegrationTest\Advertiser.Campaign.MT.IntegrationTest.VSTS.dll;CampaignManagementAPIV12\APITesterCampaignManagementV12.dll;CampaignManagementAPIV13\APITesterCampaignManagementV13.dll;ReportingAPIV13\Microsoft.Advertising.Test.Reporting.APIV13.dll;ReportingAPIV12\Microsoft.Advertising.Test.Reporting.APIV12.dll
- name: TestPlanId
  value: 931743
- name: TestRunName
  value: VSTS_SI-BVT-MT
- name: TestSuiteId
  value: 968601
- group: PSBootstrapV2
- group: AKS_InActive_ImageCleanup
- group: PSBootstrap
- group: CampaignTroubleShooterKeyVault
- group: Campaign SI Test Status
- group: batadmin_keyvault
resources:
  pipelines:
  - pipeline: 'CampaignMTTest'
    project: 'Bing_Ads'
    source: 'BingAdsCloudTest\CampaignMT\CampaignMTSIBuildDefnition-1ESPT'
    trigger: true
  repositories:
  - repository: "WebXT.Pipeline.Templates"
    type: git
    name: Engineering Fundamentals/WebXT.Pipeline.Templates
    ref: refs/heads/main
  - repository: "AdsApps_Yaml_Template"
    type: git
    name: Bing_Ads/AdsApps_Yaml_Template
    ref: refs/heads/master
extends:
  template: v1/<EMAIL>
  parameters:
    settings:
        skipSDLSourcesWhenCheckoutIsNoneForAllJobs: true
    pool:
      name: BingAds-AdsAppsMT-Hosted-EUS
      os: windows
    sdl:
      sourceAnalysisPool:
        name: Azure-Pipelines-1ESPT-ExDShared
        image: windows-latest
        os: windows
    customBuildTags:
    - ES365AIMigrationTooling-Release
    stages:
    - stage: Stage_1
      displayName: Manager Approval
      jobs:
      - job: PreDeploymentApprovalJob
        displayName: Pre-Deployment Approval
        condition: succeeded()
        timeoutInMinutes: 240
        pool: server
        steps:
        - task: ManualValidation@1
          inputs:
            notifyUsers: |-
              <EMAIL>,
              [Bing_Ads]\IPG Bing Ads Campaign Platform Release Managers,
              Build\19422243-19b9-4d85-9ca6-bc961861d287,
              <EMAIL>
            approvers: |-
              <EMAIL>,
              [Bing_Ads]\IPG Bing Ads Campaign Platform Release Managers,
              Build\19422243-19b9-4d85-9ca6-bc961861d287,
              <EMAIL>
      - job: Job_1
        displayName: Agent job
        dependsOn: PreDeploymentApprovalJob
        condition: succeeded()
        timeoutInMinutes: 0
        steps:
        - checkout: none
    - stage: Stage_2
      displayName: Set AKS Image Tag
      dependsOn: Stage_1
      jobs:
      - job: Job_1
        displayName: Agent job
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'DropMetadataCampaignMT'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest'
        steps:
        - task: PowerShell@2
          name: SetImageTagStep
          displayName: Set ImageTag from build artifacts - PSbootstrap
          inputs:
            targetType: inline
            script: |-
              $(SCRIPT_V9_IMPORT); Import-AdsAppsMTModules;
              SetMTImageTagWithGitHash -MTArtifactName "CampaignMTTest" -ImageNamePostfix "staging"
          env:
            SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    - stage: Stage_3
      displayName: Start FDP Deployment
      dependsOn: Stage_1
      jobs:
      - job: Job_1
        displayName: Agentless job
        condition: succeeded()
        timeoutInMinutes: 0
        pool: server
        steps:
        - task: Delay@1
          displayName: Delay by 10 minutes
          inputs:
            delayForMinutes: 10
    - stage: Stage_4
      displayName: '[SI] FDP-DE BCP'
      dependsOn: Stage_3
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: Azure-Pipelines-1ESPT-ExDShared
          image: ubuntu-latest
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/yaml'
        steps:
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-azure-kubelogin-inline-v3.yml@AdsApps_Yaml_Template
          parameters:
            cluster: sa-aks-si-ncus
            resourcegroup: sa-aks-si-ncus
            subscription: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
        - task: AzureCLI@2
          displayName: Azure CLI
          inputs:
            connectedServiceNameARM: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
            scriptType: bash
            scriptPath: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/deploy.sh
            scriptArguments: fd-de-si2 $(resources.pipeline.CampaignMTTest.runID)
            cwd: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/
            failOnStandardError: true
    - stage: Stage_5
      displayName: '[SI] FDP-FastBI'
      dependsOn: Stage_3
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: Azure-Pipelines-1ESPT-ExDShared
          image: ubuntu-latest
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/yaml'
        steps:
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-azure-kubelogin-inline-v3.yml@AdsApps_Yaml_Template
          parameters:
            cluster: sa-aks-si-eastus2
            resourcegroup: sa-aks-si-eastus2
            subscription: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
        - task: AzureCLI@2
          displayName: Azure CLI
          inputs:
            connectedServiceNameARM: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
            scriptType: bash
            scriptPath: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/deploy.sh
            scriptArguments: fd-fastbi-si $(resources.pipeline.CampaignMTTest.runID)
            cwd: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/
            failOnStandardError: true
    - stage: Stage_6
      displayName: '[SI] FDP-CDR'
      dependsOn: Stage_3
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: Azure-Pipelines-1ESPT-ExDShared
          image: ubuntu-latest
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/yaml'
        steps:
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-azure-kubelogin-inline-v3.yml@AdsApps_Yaml_Template
          parameters:
            cluster: sa-aks-si-eastus2
            resourcegroup: sa-aks-si-eastus2
            subscription: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
        - task: AzureCLI@2
          displayName: Azure CLI
          inputs:
            connectedServiceNameARM: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
            scriptType: bash
            scriptPath: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/deploy.sh
            scriptArguments: fd-cdr-si $(resources.pipeline.CampaignMTTest.runID)
            cwd: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/
            failOnStandardError: true
    - stage: Stage_7
      displayName: 'AKS-SI LDC2-Deployment '
      dependsOn: Stage_2
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: BingAds-CampaignMT-AKSDeployerAgents-1ESPT
          os: linux
        variables:
          ImageTag: $[ stageDependencies.Stage_2.Job_1.outputs['SetImageTagStep.ImageTag'] ]
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/BuildDrop'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'DropMetadataCampaignMT'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/DropMetadataCampaignMT'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/yaml'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'deployment'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/deployment'
        steps:
        - checkout: none
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaignmt-kubernetes-deployment-v10.yml@AdsApps_Yaml_Template
          parameters:
            artifactAlias: CampaignMTTest
            atmAction: Deploy
            cluster: cmmt-aks-eus-si-ldc2
            GlobalPriority: 2
            imageTag: $(ImageTag)
            RegionalEndpointStatus: Enabled
            subscription: CampaignPlatform-NonProd-MI
            valuesFile: values-si-ldc2.yaml
    - stage: Stage_8
      displayName: AKS-Infra SI LDC2 Deployment
      dependsOn: Stage_2
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: BingAds-CampaignMT-AKSDeployerAgents-1ESPT
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'deployment'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/deployment'
        steps:
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaign-infra-only-kubernetes-deployment-v5.yml@AdsApps_Yaml_Template
          parameters:
            chartBasePath: $(System.ArtifactsDirectory)/CampaignMTTest/deployment/charts
            cluster: cmmt-aks-eus-si-ldc2
            grafana_hostname: si-ldc2
            subscription: CampaignPlatform-NonProd-MI
            valuesFile: values-si-ldc2.yaml
    - stage: Stage_9
      displayName: '[SI] FDP-DE'
      dependsOn: Stage_4
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: Azure-Pipelines-1ESPT-ExDShared
          image: ubuntu-latest
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/yaml'
        steps:
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-azure-kubelogin-inline-v3.yml@AdsApps_Yaml_Template
          parameters:
            cluster: sa-aks-si-eastus2
            resourcegroup: sa-aks-si-eastus2
            subscription: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
        - task: AzureCLI@2
          displayName: Azure CLI
          inputs:
            connectedServiceNameARM: StagingArea-AKS-SI-PME-WorkloadIdentity-ARM
            scriptType: bash
            scriptPath: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/deploy.sh
            scriptArguments: fd-de-si $(resources.pipeline.CampaignMTTest.runID)
            cwd: $(System.ArtifactsDirectory)/CampaignMTTest/yaml/
            failOnStandardError: true
    - stage: Stage_10
      displayName: Tag MT_SI_LDC2
      dependsOn: Stage_7
      jobs:
      - job: Job_1
        displayName: Agent job
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop'
        steps:
        - task: PowerShell@2
          displayName: Get build drop url
          inputs:
            targetType: inline
            script: |-
              $DropPathFile = "$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop/drop.txt"
              $BuildUrl = [IO.File]::ReadAllText($DropPathFile).Trim()
              Write-Host "##vso[task.setvariable variable=buildDropUrl]$BuildUrl"
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-add-tag-to-commit-from-build-drop-url-v2.yml@AdsApps_Yaml_Template
          parameters:
            buildDropUrl: $(buildDropUrl)
            tag: MT_SI_LDC2
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-add-tag-to-commit-from-build-drop-url-v2.yml@AdsApps_Yaml_Template
          parameters:
            buildDropUrl: $(buildDropUrl)
            tag: MT_STAGING
    - stage: Stage_11
      displayName: AKS SI LDC1 Deployment
      dependsOn:
      - Stage_7
      - Stage_2
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: BingAds-CampaignMT-AKSDeployerAgents-1ESPT
          os: linux
        variables:
          ImageTag: $[ stageDependencies.Stage_2.Job_1.outputs['SetImageTagStep.ImageTag'] ]
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/BuildDrop'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'DropMetadataCampaignMT'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/DropMetadataCampaignMT'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/yaml'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'deployment'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/deployment'
        steps:
        - checkout: none
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaignmt-kubernetes-deployment-v10.yml@AdsApps_Yaml_Template
          parameters:
            artifactAlias: CampaignMTTest
            atmAction: Deploy
            cluster: cmmt-aks-eus-si-ldc1
            GlobalPriority: 1
            imageTag: $(ImageTag)
            RegionalEndpointStatus: Enabled
            subscription: CampaignPlatform-NonProd-MI
            valuesFile: values-si-ldc1.yaml
    - stage: Stage_12
      displayName: AKS-Infra SI LDC1 Deployment
      dependsOn:
      - Stage_8
      - Stage_7
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: BingAds-CampaignMT-AKSDeployerAgents-1ESPT
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'deployment'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/deployment'
        steps:
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaign-infra-only-kubernetes-deployment-v5.yml@AdsApps_Yaml_Template
          parameters:
            chartBasePath: $(System.ArtifactsDirectory)/CampaignMTTest/deployment/charts
            cluster: cmmt-aks-eus-si-ldc1
            grafana_hostname: si-ldc1
            subscription: CampaignPlatform-NonProd-MI
            valuesFile: values-si-ldc1.yaml
    - stage: Stage_13
      displayName: Trigger Campaign SI Deployment Status Update
      dependsOn:
      - Stage_10
      - Stage_5
      - Stage_6
      - Stage_9
      jobs:
      - job: Job_1
        displayName: Agent job
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop'
        steps:
        - task: PowerShell@2
          displayName: Get build drop url
          inputs:
            targetType: inline
            script: |-
              $DropPathFile = "$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop/drop.txt"
              $BuildUrl = [IO.File]::ReadAllText($DropPathFile).Trim()
              Write-Host "##vso[task.setvariable variable=buildDropUrl]$BuildUrl"
        - task: benjhuser.tfs-extensions-build-tasks.trigger-build-task.TriggerBuild@3
          displayName: Trigger a new build of Campaign SI Deployment Status Update (14709)
          inputs:
            buildDefinition: 14709
            buildParameters: '\"ServiceStatusName\":\"DeploySuccessMT\",\"ReleaseUrl\":\"$(Release.ReleaseWebURL)\",\"BuildDropUrl\":\"$(buildDropUrl)\"'
            useSameBranch: false
            branchToUse: master
            password: $(System.AccessToken)
    - stage: Stage_14
      displayName: Tag MT_SI_LDC1, Notify UI
      dependsOn: Stage_11
      jobs:
      - job: Job_1
        displayName: Agent job
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop'
        steps:
        - task: PowerShell@2
          displayName: Get build drop url
          inputs:
            targetType: inline
            script: |-
              $DropPathFile = "$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop/drop.txt"
              $BuildUrl = [IO.File]::ReadAllText($DropPathFile).Trim()
              Write-Host "##vso[task.setvariable variable=buildDropUrl]$BuildUrl"
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-add-tag-to-commit-from-build-drop-url-v2.yml@AdsApps_Yaml_Template
          parameters:
            buildDropUrl: $(buildDropUrl)
            tag: MT_SI_LDC1
    - stage: Stage_15
      displayName: SI Sandbox Application Insight Validation
      dependsOn: Stage_14
      jobs:
      - job: Job_1
        displayName: Agent job
        condition: succeeded()
        timeoutInMinutes: 0
        steps:
        - checkout: none
        - task: PowerShell@2
          displayName: SI Sandbox Application Insight Validation
          inputs:
            targetType: inline
            script: |-
              # Write your PowerShell commands here.
              Write-Host "SI Sandbox Application Insight (Gates) Validation finished"
    - stage: Stage_16
      displayName: Signoff SI Build
      dependsOn: Stage_13
      jobs:
      - job: PreDeploymentApprovalJob
        displayName: Pre-Deployment Approval
        condition: succeeded()
        timeoutInMinutes: 1440
        pool: server
        steps:
        - task: ManualValidation@1
          inputs:
            notifyUsers: |-
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>,
              <EMAIL>
            approvers: |-
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>,
              <EMAIL>
      - job: Job_1
        displayName: Agent job
        dependsOn: PreDeploymentApprovalJob
        condition: succeeded()
        timeoutInMinutes: 0
        steps:
        - checkout: none
        - task: Bash@3
          displayName: Bash Script
          inputs:
            targetType: inline
            script: |
              # Write your commands here
              echo 'A dummy job to simulate si -> prod gate'
    - stage: Stage_17
      displayName: Check SI Test
      dependsOn: Stage_13
      jobs:
      - job: PreDeploymentApprovalJob
        displayName: Pre-Deployment Approval
        condition: succeeded()
        timeoutInMinutes: 1440
        pool: server
        steps:
        - task: ManualValidation@1
          inputs:
            notifyUsers: |-
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>,
              <EMAIL>
            approvers: |-
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>,
              <EMAIL>
      - job: Job_1
        displayName: Agent job
        dependsOn: PreDeploymentApprovalJob
        condition: succeeded()
        timeoutInMinutes: 0
        steps:
        - checkout: none
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-pipeline-submitpipelinemetricstokusto-v2.yml@AdsApps_Yaml_Template
          parameters:
            MetricData: null
            MetricType: MTSITest
        - task: PowerShell@2
          displayName: Check whether SITest Passed
          inputs:
            targetType: inline
            script: |
              $(SCRIPT_V9_IMPORT); Import-AdsAppsCloudTestModules ;
              $downloadDir= _GetDownloadRootDir "ADSAPPSCLOUDTEST";
              Write-host "downloadDir : $downloadDir"
              $scriptRoot="$downloadDir/dev-msasg-Bing_Ads-AdsApps_CloudTest/private/CloudTest"
              cd $scriptRoot/VSTS/
              while ($True) {
                  .\SITestResults\CheckLatestSITestResult.ps1 -TestBuckets "$(allTestNames)"  -SITestVariableGroupId $(SITestVariableGroupId)
                  if ( $LASTEXITCODE -eq 0 ){
                     break
                  }
                  Start-Sleep -Seconds 900
              }
          timeoutInMinutes: 600
          env:
            BatadminPAT: $(System.AccessToken)
            SYSTEM_ACCESSTOKEN: $(System.AccessToken)
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-pipeline-submitpipelinemetricstokusto-v2.yml@AdsApps_Yaml_Template
          parameters:
            MetricData: Passed
            MetricType: MTSITest
    - stage: Stage_18
      displayName: Check SI NewError
      dependsOn: Stage_13
      jobs:
      - job: PreDeploymentApprovalJob
        displayName: Pre-Deployment Approval
        condition: succeeded()
        timeoutInMinutes: 1440
        pool: server
        steps:
        - task: ManualValidation@1
          inputs:
            notifyUsers: |-
              <EMAIL>,
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>
            approvers: |-
              <EMAIL>,
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>
      - job: Job_1
        displayName: Agent job
        dependsOn: PreDeploymentApprovalJob
        condition: succeeded()
        timeoutInMinutes: 0
        steps:
        - checkout: none
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-pipeline-submitpipelinemetricstokusto-v2.yml@AdsApps_Yaml_Template
          parameters:
            MetricData: null
            MetricType: MTSINewError
        - task: PowerShell@2
          displayName: Trigger & Check SINewError Report
          inputs:
            targetType: inline
            script: "$CampaignTroubleShooterAccessToken=\"$(CampaignTroubleShooterAccessToken)\"\n\nWrite-host \"Trigger SI New Error Report\"\n$url=\"https://campaigntroubleshooterpme.azurewebsites.net/SIReport\"\n$reportId=Invoke-RestMethod -Uri $url -method GET -headers @{Authorization=(\"Bearer {0}\" -f $CampaignTroubleShooterAccessToken)} -ContentType \"application/json\"\n\nStart-Sleep -Seconds 300\n\nWrite-host \"Check whether SI Report Generated\"\n$url=\"https://campaigntroubleshooterpme.azurewebsites.net/SICIMonitor/$reportId/index.html\"\nwrite-host \"report url $url\"\n$releaseId=$(Release.ReleaseId)\nwrite-host \"releaseid $releaseId\"\n\n$SINewErrorCheck=$False\n\nWhile ($True) {\n\n    try{\n        $result=Invoke-RestMethod -Uri $url -method GET -headers @{Authorization=(\"Bearer {0}\" -f $CampaignTroubleShooterAccessToken)} -ContentType \"application/json\"\n    } catch {\n        Write-Host \"Error: $($_.Exception.Message)\"\n        $statusCode = $_.Exception.Response.StatusCode.Value__\n        Write-Host \"HTTP status code: $statusCode\"\n    }\n    Write-host \"result: $result\"\n    if ($result) {\n       if ($result.ReleaseId -ne $releaseId ) {\n                write-host \"This report is not the most recent version. Need to wait for the newer one to be generated\"\n                continue\n        }\n        $SINewErrorCheck=$result.Pass\n        $ReportIndex=$result.RunCount\n        write-host \"Full Report Link: https://campaigntroubleshooterpme.azurewebsites.net/SICIMonitor/$releaseId/$ReportIndex\"\n\n        if (-not $SINewErrorCheck){\n            write-host \"New SI Error detected:\"\n            write-host $result.SummaryNewErrors\n            exit 1\n        } \n        \n        write-host \"No new SI Error detected\"\n        exit 0\n    }\n\n    Start-Sleep -Seconds 120\n}\n"
          timeoutInMinutes: 120
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-pipeline-submitpipelinemetricstokusto-v2.yml@AdsApps_Yaml_Template
          parameters:
            MetricData: Passed
            MetricType: MTSINewError
    - stage: Stage_19
      displayName: Queue Production Build
      dependsOn:
      - Stage_15
      - Stage_2
      jobs:
      - job: PreDeploymentApprovalJob
        displayName: Pre-Deployment Approval
        condition: succeeded()
        timeoutInMinutes: 0
        pool: server
        steps:
        - task: ManualValidation@1
          inputs:
            notifyUsers: |-
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>
            approvers: |-
              [Bing_Ads]\IPG Bing Ads Campaign Platform Deployment Owners,
              <EMAIL>
      - job: Job_1
        displayName: Agent phase
        variables:
          ImageTag: $[ stageDependencies.Stage_2.Job_1.outputs['SetImageTagStep.ImageTag'] ]
        dependsOn: PreDeploymentApprovalJob
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop'
        steps:
        - task: PowerShell@2
          displayName: Queue Production Build
          inputs:
            targetType: inline
            script: |
              $DropPathFile = "$(System.ArtifactsDirectory)/CampaignMTTest/BuildDrop/drop.txt"
              $BuildUrl = [IO.File]::ReadAllText($DropPathFile).Trim()
              Write-Host "Build -" $BuildUrl
              #Get previous image id from build artifacts id. 
              $retagFromPreviousImage = "$(ImageTag)"
              Write-Host "RetagFromPreviousImage -" $retagFromPreviousImage
              $user='batadmin'
              $token=$env:token
              # Base64-encodes the Personal Access Token (PAT) appropriately
              $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $user,$token)))
              $buildDefinitionId = 32959
              $lkgArgName='APAdCoreCampaignMT'
              $retagImageArgName='RetagFromPreviousImage'
              $projectUrl = $Env:SYSTEM_TEAMFOUNDATIONCOLLECTIONURI + $Env:SYSTEM_TEAMPROJECT
              # Queue Production Build
              $body = '{ "definition": { "id":'+$buildDefinitionId+'}, "parameters":"{\"'+$lkgArgName+'\":\"'+$BuildUrl+'\",\"'+$retagImageArgName +'\":\"'+ $retagFromPreviousImage + '\"}"}'
              write-host $body
              $queueUri = $projectUrl + '/_apis/build/builds?api-version=2.0'
              $responsebody =Invoke-RestMethod -Uri $queueUri -Method Post -headers @{Authorization=("Basic {0}" -f $base64AuthInfo)} -ContentType "application/json" -body $body
              $buildId = [string]$responsebody.id
               Write-Host "BUILD ID: $buildId"
          env:
            token: $(System.AccessToken)
    - stage: Stage_20
      displayName: Auto-SignOff SI Build
      dependsOn:
      - Stage_17
      - Stage_18
      jobs:
      - job: Job_1
        displayName: Agent job
        condition: succeeded()
        timeoutInMinutes: 0
        steps:
        - checkout: none
        - task: PowerShell@2
          displayName: Auto-approve "SignOff  SI Build" job
          inputs:
            targetType: inline
            script: |
              $(SCRIPT_V9_IMPORT); Import-AdsAppsCloudTestModules -PAT $(BatadminPAT)
              $downloadDir= _GetDownloadRootDir "ADSAPPSCLOUDTEST";
              Write-host "downloadDir : $downloadDir"
              $scriptRoot="$downloadDir/dev-msasg-Bing_Ads-AdsApps_CloudTest/private/CloudTest"
              cd $scriptRoot/VSTS/
              .\ApproveReleaseStage.ps1 -stageName "Signoff SI Build"
          env:
            BatadminPAT: $(System.AccessToken)
            SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    - stage: Stage_21
      displayName: AKS-SI LDC2-ArgoRollout
      trigger: manual
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: BingAds-CampaignMT-AKSDeployerAgents-1ESPT
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/BuildDrop'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'DropMetadataCampaignMT'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/DropMetadataCampaignMT'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/yaml'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'deployment'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/deployment'
        steps:
        - task: PowerShell@2
          name: SetImageTagStep
          displayName: Set ImageTag from build artifacts - PSbootstrap
          inputs:
            targetType: inline
            script: |-
              $(SCRIPT_V9_IMPORT); Import-AdsAppsMTModules;
              SetMTImageTagWithGitHash -MTArtifactName "CampaignMTTest" -ImageNamePostfix "staging"
          env:
            SYSTEM_ACCESSTOKEN: $(System.AccessToken)
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaignmt-kubernetes-deployment-v10.yml@AdsApps_Yaml_Template
          parameters:
            argoRolloutEnabled: true
            artifactAlias: CampaignMTTest
            atmAction: Deploy
            ATMProfileCustomizedPostfix: campaignmt-svc
            cluster: cmmt-aks-eus-si-ldc2
            GlobalPriority: 2
            imageTag: $(SetImageTagStep.ImageTag)
            LogicalEndpointStatus: Disabled
            namespace: campaignmt
            pilot: true
            RegionalEndpointStatus: Enabled
            subscription: CampaignPlatform-NonProd-MI
            valuesFile: values-si-ldc2.yaml
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-aks-deployment-argorolloutfuncs-v1.yml@AdsApps_Yaml_Template
          parameters:
            cluster: cmmt-aks-eus-si-ldc2
            ImageTag: $(SetImageTagStep.ImageTag)
            option: Validate
            resourcegroup: cmmt-aks-eus-si-ldc2
            subscription: CampaignPlatform-NonProd-MI
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaignmt-kubernetes-universalconfig-argolaunchsupport-v2.yml@AdsApps_Yaml_Template
          parameters:
            Action: Disable
            ArtifactDirectory: CampaignMTTest
            Cluster: cmmt-aks-eus-si-ldc2
            Kind: rollouts
            namespace: campaignmt
            Subscription: CampaignPlatform-NonProd-MI
    - stage: Stage_22
      displayName: AKS-SI LDC1-ArgoRollout
      trigger: manual
      jobs:
      - job: Job_1
        displayName: Agent job
        pool:
          name: BingAds-CampaignMT-AKSDeployerAgents-1ESPT
          os: linux
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'BuildDrop'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/BuildDrop'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'DropMetadataCampaignMT'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/DropMetadataCampaignMT'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'yaml'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/yaml'
          - input: pipelineArtifact
            pipeline: 'CampaignMTTest'
            artifactName: 'deployment'
            targetPath: '$(System.DefaultWorkingDirectory)/CampaignMTTest/deployment'
        steps:
        - task: PowerShell@2
          name: SetImageTagStep
          displayName: Set ImageTag from build artifacts - PSbootstrap
          inputs:
            targetType: inline
            script: |-
              $(SCRIPT_V9_IMPORT); Import-AdsAppsMTModules;
              SetMTImageTagWithGitHash -MTArtifactName "CampaignMTTest" -ImageNamePostfix "staging"
          env:
            SYSTEM_ACCESSTOKEN: $(System.AccessToken)
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaignmt-kubernetes-deployment-v10.yml@AdsApps_Yaml_Template
          parameters:
            argoRolloutEnabled: true
            artifactAlias: CampaignMTTest
            atmAction: Deploy
            ATMProfileCustomizedPostfix: campaignmt-svc
            cluster: cmmt-aks-eus-si-ldc1
            GlobalPriority: 1
            imageTag: $(SetImageTagStep.ImageTag)
            LogicalEndpointStatus: Disabled
            namespace: campaignmt
            pilot: true
            RegionalEndpointStatus: Enabled
            subscription: CampaignPlatform-NonProd-MI
            valuesFile: values-si-ldc1.yaml
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-bingads-aks-deployment-argorolloutfuncs-v1.yml@AdsApps_Yaml_Template
          parameters:
            cluster: cmmt-aks-eus-si-ldc1
            ImageTag: $(SetImageTagStep.ImageTag)
            option: Validate
            resourcegroup: cmmt-aks-eus-si-ldc1
            subscription: CampaignPlatform-NonProd-MI
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaignmt-kubernetes-universalconfig-argolaunchsupport-v2.yml@AdsApps_Yaml_Template
          parameters:
            Action: Disable
            ArtifactDirectory: CampaignMTTest
            Cluster: cmmt-aks-eus-si-ldc1
            Kind: rollouts
            namespace: campaignmt
            Subscription: CampaignPlatform-NonProd-MI
    - stage: Stage_23
      displayName: Auto-Approve Manager Approval
      dependsOn: []
      jobs:
      - job: Job_1
        displayName: Agent job
        condition: succeeded()
        timeoutInMinutes: 0
        steps:
        - checkout: none
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-download-vsts-scripts-from-cloudtest-repo-v2.yml@AdsApps_Yaml_Template
        - task: PowerShell@2
          displayName: Approve Release if Triggered by BatAdmin
          condition: "and(succeeded(),  or(eq(variables['Release.RequestedForEmail'], '<EMAIL>'), eq(variables['Release.RequestedFor'], 'Project Collection Build Service (msasg)'))) "
          inputs:
            filePath: $(System.ArtifactsDirectory)/drop/retail/amd64/CloudTest/VSTS/ApproveReleaseStage.ps1
            arguments: -stageName "Manager Approval"
