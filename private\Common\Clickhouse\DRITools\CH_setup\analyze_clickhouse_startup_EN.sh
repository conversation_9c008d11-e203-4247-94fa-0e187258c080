#!/bin/bash
# ClickHouse Startup Log Analysis Script
# Usage: ./analyze_clickhouse_startup_EN.sh <clickhouse_log_file>

set -e

if [ $# -lt 1 ]; then
    echo "Usage: $0 <clickhouse_log_file>"
    exit 1
fi

LOG_FILE="$1"
OUTPUT_DIR="clickhouse_analysis_$(date +%Y%m%d_%H%M%S)"

if [ ! -f "$LOG_FILE" ]; then
    echo "Error: Log file $LOG_FILE not found"
    exit 1
fi

echo "Starting analysis of ClickHouse startup log: $LOG_FILE"
echo "Results will be saved to directory: $OUTPUT_DIR"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Extract startup time range
echo "Extracting startup time range..."
START_TIME=$(head -n 1 "$LOG_FILE" | grep -oE '[0-9]{2}:[0-9]{2}:[0-9]{2}')
READY_LINE=$(grep -n "Ready for connections" "$LOG_FILE" | head -1)
if [ -n "$READY_LINE" ]; then
    READY_LINE_NUM=$(echo "$READY_LINE" | cut -d':' -f1)
    READY_TIME=$(echo "$READY_LINE" | grep -oE '[0-9]{2}:[0-9]{2}:[0-9]{2}')
    
    # Calculate startup time (simplified, doesn't handle cross-day)
    START_SECONDS=$(date -d "$START_TIME" +%s)
    READY_SECONDS=$(date -d "$READY_TIME" +%s)
    STARTUP_DURATION=$((READY_SECONDS - START_SECONDS))
    
    echo "Startup time: $START_TIME" | tee "$OUTPUT_DIR/startup_summary.txt"
    echo "Ready time: $READY_TIME" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    echo "Startup duration: $STARTUP_DURATION seconds" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    echo "" | tee -a "$OUTPUT_DIR/startup_summary.txt"
else
    echo "Warning: Startup completion marker not found" | tee "$OUTPUT_DIR/startup_summary.txt"
fi

# Analyze part loading
echo "Analyzing part loading..."
grep "Loaded data part" "$LOG_FILE" > "$OUTPUT_DIR/parts_loaded.log"
TOTAL_PARTS=$(wc -l < "$OUTPUT_DIR/parts_loaded.log")

echo "Part Loading Analysis:" | tee -a "$OUTPUT_DIR/startup_summary.txt"
echo "Total parts loaded: $TOTAL_PARTS" | tee -a "$OUTPUT_DIR/startup_summary.txt"

# Statistics by table
echo "Analyzing part loading count and time by table..." | tee -a "$OUTPUT_DIR/startup_summary.txt"
grep "Loaded data part" "$LOG_FILE" | 
    sed -E 's/.*Loaded data part [^ ]+ from ([^ ]+) in ([0-9.]+) ms.*/\1 \2/' | 
    awk '{
        count[$1]++; 
        time[$1]+=$2; 
    } 
    END {
        printf "%-60s %-10s %-15s %-15s\n", "Table Name", "Part Count", "Total Time(ms)", "Avg Time(ms)";
        for (table in count) {
            printf "%-60s %-10d %-15.1f %-15.1f\n", table, count[table], time[table], time[table]/count[table];
        }
    }' | sort -k3 -nr > "$OUTPUT_DIR/parts_by_table.txt"

head -n 20 "$OUTPUT_DIR/parts_by_table.txt" | tee -a "$OUTPUT_DIR/startup_summary.txt"
echo "" | tee -a "$OUTPUT_DIR/startup_summary.txt"

# Analyze mutation operations
echo "Analyzing mutation operations..."
grep "Mutation.*took" "$LOG_FILE" > "$OUTPUT_DIR/mutations.log"
TOTAL_MUTATIONS=$(wc -l < "$OUTPUT_DIR/mutations.log")

if [ $TOTAL_MUTATIONS -gt 0 ]; then
    echo "Mutation Analysis:" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    echo "Total mutations: $TOTAL_MUTATIONS" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    
    # Statistics by table
    echo "Analyzing mutations by table..." | tee -a "$OUTPUT_DIR/startup_summary.txt"
    grep "Mutation.*took" "$LOG_FILE" | 
        sed -E 's/.*Mutation [^ ]+ for table ([^ ]+) took ([0-9.]+) ms.*/\1 \2/' | 
        awk '{
            count[$1]++; 
            time[$1]+=$2; 
        } 
        END {
            printf "%-60s %-10s %-15s %-15s\n", "Table Name", "Mutation Count", "Total Time(ms)", "Avg Time(ms)";
            for (table in count) {
                printf "%-60s %-10d %-15.1f %-15.1f\n", table, count[table], time[table], time[table]/count[table];
            }
        }' | sort -k3 -nr > "$OUTPUT_DIR/mutations_by_table.txt"
    
    head -n 10 "$OUTPUT_DIR/mutations_by_table.txt" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    echo "" | tee -a "$OUTPUT_DIR/startup_summary.txt"
fi

# Analyze background jobs
echo "Analyzing background jobs..."
grep "Finished background job" "$LOG_FILE" > "$OUTPUT_DIR/background_jobs.log"
TOTAL_JOBS=$(wc -l < "$OUTPUT_DIR/background_jobs.log")

if [ $TOTAL_JOBS -gt 0 ]; then
    echo "Background Job Analysis:" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    echo "Total background jobs: $TOTAL_JOBS" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    
    # Statistics by job type
    echo "Analyzing jobs by type..." | tee -a "$OUTPUT_DIR/startup_summary.txt"
    grep "Finished background job" "$LOG_FILE" | 
        sed -E 's/.*Finished background job ([^ ]+) in ([0-9.]+) ms.*/\1 \2/' | 
        awk '{
            count[$1]++; 
            time[$1]+=$2; 
        } 
        END {
            printf "%-40s %-10s %-15s %-15s\n", "Job Type", "Count", "Total Time(ms)", "Avg Time(ms)";
            for (job in count) {
                printf "%-40s %-10d %-15.1f %-15.1f\n", job, count[job], time[job], time[job]/count[job];
            }
        }' | sort -k3 -nr > "$OUTPUT_DIR/jobs_by_type.txt"
    
    head -n 10 "$OUTPUT_DIR/jobs_by_type.txt" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    echo "" | tee -a "$OUTPUT_DIR/startup_summary.txt"
fi

# Analyze table attach operations
echo "Analyzing table attach operations..."
grep "Attaching table" "$LOG_FILE" > "$OUTPUT_DIR/table_attach.log"
TOTAL_TABLES=$(wc -l < "$OUTPUT_DIR/table_attach.log")

echo "Table Attach Analysis:" | tee -a "$OUTPUT_DIR/startup_summary.txt"
echo "Total tables attached: $TOTAL_TABLES" | tee -a "$OUTPUT_DIR/startup_summary.txt"

# Count tables by database
echo "Counting tables by database..." | tee -a "$OUTPUT_DIR/startup_summary.txt"
grep "Attaching table" "$LOG_FILE" | 
    sed -E 's/.*Attaching table ([^.]+)\..*/\1/' | 
    sort | uniq -c | sort -nr | 
    awk '{printf "%-30s %d\n", $2, $1}' > "$OUTPUT_DIR/tables_by_database.txt"

head -n 10 "$OUTPUT_DIR/tables_by_database.txt" | tee -a "$OUTPUT_DIR/startup_summary.txt"
echo "" | tee -a "$OUTPUT_DIR/startup_summary.txt"

# Analyze errors and warnings
echo "Analyzing errors and warnings..."
grep -E "<Error>" "$LOG_FILE" > "$OUTPUT_DIR/errors.log"
grep -E "<Warning>" "$LOG_FILE" > "$OUTPUT_DIR/warnings.log"
TOTAL_ERRORS=$(wc -l < "$OUTPUT_DIR/errors.log")
TOTAL_WARNINGS=$(wc -l < "$OUTPUT_DIR/warnings.log")

echo "Error and Warning Analysis:" | tee -a "$OUTPUT_DIR/startup_summary.txt"
echo "Total errors: $TOTAL_ERRORS" | tee -a "$OUTPUT_DIR/startup_summary.txt"
echo "Total warnings: $TOTAL_WARNINGS" | tee -a "$OUTPUT_DIR/startup_summary.txt"

# Analyze memory usage
echo "Analyzing memory usage..."
grep "Memory usage:" "$LOG_FILE" > "$OUTPUT_DIR/memory_usage.log"

if [ -s "$OUTPUT_DIR/memory_usage.log" ]; then
    # Extract maximum memory usage
    MAX_MEMORY=$(grep "Memory usage:" "$LOG_FILE" | 
                 sed -E 's/.*Memory usage: ([0-9.]+) (MB|GB).*/\1 \2/' | 
                 awk '{
                     if ($2 == "GB") { 
                         mem = $1 * 1024; 
                     } else { 
                         mem = $1; 
                     } 
                     if (mem > max) max = mem;
                 } 
                 END {
                     print max;
                 }')
    
    echo "Memory Usage Analysis:" | tee -a "$OUTPUT_DIR/startup_summary.txt"
    echo "Maximum memory usage: $MAX_MEMORY MB" | tee -a "$OUTPUT_DIR/startup_summary.txt"
fi

# Generate timeline analysis
echo "Generating timeline analysis..."
{
    echo "Time,Event,Details"
    
    # Part loading events
    grep "Loaded data part" "$LOG_FILE" | 
        head -n 1000 |
        sed -E 's/([0-9]{2}:[0-9]{2}:[0-9]{2}).*Loaded data part ([^ ]+) from ([^ ]+) in ([0-9.]+) ms.*/\1,Part Loading,\3 (\4ms)/' 
    
    # Mutation events
    grep "Mutation.*took" "$LOG_FILE" | 
        sed -E 's/([0-9]{2}:[0-9]{2}:[0-9]{2}).*Mutation ([^ ]+) for table ([^ ]+) took ([0-9.]+) ms.*/\1,Mutation,\3 (\4ms)/' 
    
    # Background job events
    grep "Finished background job" "$LOG_FILE" | 
        sed -E 's/([0-9]{2}:[0-9]{2}:[0-9]{2}).*Finished background job ([^ ]+) in ([0-9.]+) ms.*/\1,Background Job,\2 (\3ms)/' 
    
    # Table attach events
    grep "Attaching table" "$LOG_FILE" | 
        sed -E 's/([0-9]{2}:[0-9]{2}:[0-9]{2}).*Attaching table ([^ ]+).*/\1,Table Attach,\2/' 
    
} | sort -t, -k1 > "$OUTPUT_DIR/timeline.csv"

echo "Analysis complete! Results saved in $OUTPUT_DIR directory"
echo "Summary report: $OUTPUT_DIR/startup_summary.txt"
