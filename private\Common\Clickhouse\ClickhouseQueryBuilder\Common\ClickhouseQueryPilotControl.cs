﻿using Microsoft.AdCenter.Shared.MT;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public class ClickhouseQueryPilotControl
    {
        public static bool IsQueryTypeEnabled(string procName, DBType dBType)
        {
            if (string.IsNullOrEmpty(procName))
            {
                return false;
            }
            procName = procName.Replace("dbo.", "");
            return Enum.TryParse<ClickhouseQueryType>(procName, true, out var queryType);
        }
    }
}
