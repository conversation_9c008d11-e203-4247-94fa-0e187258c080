#!/usr/bin/env python3
"""
ClickHouse Table-Level Performance Analysis
Analyzes table loading performance and time distribution
"""

import re
import sys
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter

def parse_time(time_str):
    """Parse time string to datetime object"""
    try:
        return datetime.strptime(time_str, '%H:%M:%S.%f')
    except ValueError:
        return datetime.strptime(time_str, '%H:%M:%S')

def calculate_duration(start_time, end_time):
    """Calculate duration between two times (seconds)"""
    start = parse_time(start_time)
    end = parse_time(end_time)
    
    # Handle cross-day scenarios
    if end < start:
        end = end + timedelta(days=1)
    
    return (end - start).total_seconds()

def analyze_table_performance(log_file):
    """Analyze table-level performance"""
    
    print(f"Starting table performance analysis: {log_file}")
    
    # Data structures
    table_timings = defaultdict(lambda: {
        'schedule_time': None,
        'execute_time': None,
        'finish_time': None,
        'status': 'unknown',
        'duration': 0,
        'events': []
    })
    
    database_stats = defaultdict(lambda: {
        'table_count': 0,
        'total_duration': 0,
        'completed_tables': 0,
        'failed_tables': 0,
        'tables': []
    })
    
    startup_phases = []
    errors_by_table = defaultdict(list)
    
    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
        line_count = 0
        for line in f:
            line_count += 1
            if line_count % 50000 == 0:
                print(f"Processed {line_count:,} lines...")
            
            # Extract timestamp
            timestamp_match = re.search(r'(\d{2}:\d{2}:\d{2}\.\d{6})', line)
            if not timestamp_match:
                continue
            
            timestamp = timestamp_match.group(1)
            
            # Analyze table loading events
            if 'load table' in line:
                # Extract table name
                table_match = re.search(r"load table ([^']+)", line)
                if table_match:
                    table_name = table_match.group(1).strip("'")
                    
                    # Determine database name
                    if '.' in table_name:
                        db_name = table_name.split('.')[0]
                    else:
                        db_name = 'unknown'
                    
                    # Record events
                    if 'Schedule' in line:
                        table_timings[table_name]['schedule_time'] = timestamp
                        table_timings[table_name]['events'].append({
                            'time': timestamp,
                            'action': 'scheduled'
                        })
                        database_stats[db_name]['table_count'] += 1
                        database_stats[db_name]['tables'].append(table_name)
                        
                    elif 'Execute' in line:
                        table_timings[table_name]['execute_time'] = timestamp
                        table_timings[table_name]['events'].append({
                            'time': timestamp,
                            'action': 'execute'
                        })
                        
                    elif 'Finish' in line:
                        table_timings[table_name]['finish_time'] = timestamp
                        
                        # Extract status
                        status_match = re.search(r'with status (\w+)', line)
                        if status_match:
                            status = status_match.group(1)
                            table_timings[table_name]['status'] = status
                            
                            if status == 'OK':
                                database_stats[db_name]['completed_tables'] += 1
                            else:
                                database_stats[db_name]['failed_tables'] += 1
                        
                        table_timings[table_name]['events'].append({
                            'time': timestamp,
                            'action': 'finish',
                            'status': status if status_match else 'unknown'
                        })
                        
                        # Calculate duration
                        if table_timings[table_name]['execute_time']:
                            duration = calculate_duration(
                                table_timings[table_name]['execute_time'],
                                timestamp
                            )
                            table_timings[table_name]['duration'] = duration
                            database_stats[db_name]['total_duration'] += duration
            
            # Record startup phases
            if 'database' in line.lower() and 'has' in line and 'tables' in line:
                db_match = re.search(r'database (\w+) has (\d+) tables', line)
                if db_match:
                    startup_phases.append({
                        'time': timestamp,
                        'phase': f"Database {db_match.group(1)} loaded",
                        'details': f"{db_match.group(2)} tables"
                    })
            
            if 'Ready for connections' in line:
                startup_phases.append({
                    'time': timestamp,
                    'phase': 'Ready for connections',
                    'details': 'Startup complete'
                })
            
            # Record table-related errors
            if '<Error>' in line and 'table' in line.lower():
                table_match = re.search(r'table (\S+)', line, re.IGNORECASE)
                if table_match:
                    table_name = table_match.group(1)
                    errors_by_table[table_name].append({
                        'time': timestamp,
                        'error': line.strip()
                    })
    
    print(f"Analysis complete, processed {line_count:,} lines")
    
    return {
        'table_timings': dict(table_timings),
        'database_stats': dict(database_stats),
        'startup_phases': startup_phases,
        'errors_by_table': dict(errors_by_table)
    }

def generate_performance_report(data):
    """Generate performance report"""
    
    report = []
    report.append("=" * 100)
    report.append("ClickHouse Table-Level Performance Analysis Report")
    report.append("=" * 100)
    
    # Database-level statistics
    report.append(f"\n📊 Database-Level Statistics:")
    report.append("-" * 80)
    
    total_tables = 0
    total_completed = 0
    total_failed = 0
    total_duration = 0
    
    for db_name, stats in data['database_stats'].items():
        total_tables += stats['table_count']
        total_completed += stats['completed_tables']
        total_failed += stats['failed_tables']
        total_duration += stats['total_duration']
        
        avg_duration = stats['total_duration'] / max(stats['completed_tables'], 1)
        success_rate = (stats['completed_tables'] / max(stats['table_count'], 1)) * 100
        
        report.append(f"Database: {db_name}")
        report.append(f"  Table Count: {stats['table_count']}")
        report.append(f"  Successfully Loaded: {stats['completed_tables']}")
        report.append(f"  Failed: {stats['failed_tables']}")
        report.append(f"  Success Rate: {success_rate:.1f}%")
        report.append(f"  Total Duration: {stats['total_duration']:.2f} seconds")
        report.append(f"  Average Duration: {avg_duration:.3f} seconds/table")
        report.append("")
    
    # Overall statistics
    report.append(f"📈 Overall Statistics:")
    report.append("-" * 80)
    report.append(f"Total Tables: {total_tables}")
    report.append(f"Successfully Loaded: {total_completed}")
    report.append(f"Failed to Load: {total_failed}")
    report.append(f"Overall Success Rate: {(total_completed / max(total_tables, 1)) * 100:.1f}%")
    report.append(f"Total Loading Time: {total_duration:.2f} seconds")
    report.append(f"Average Loading Time: {total_duration / max(total_completed, 1):.3f} seconds/table")
    
    # Slowest tables
    report.append(f"\n🐌 Slowest Loading Tables (Top 20):")
    report.append("-" * 80)
    
    # Filter valid table timing data
    valid_tables = {name: timing for name, timing in data['table_timings'].items() 
                   if timing['duration'] > 0 and timing['status'] == 'OK'}
    
    if valid_tables:
        sorted_tables = sorted(valid_tables.items(), key=lambda x: x[1]['duration'], reverse=True)
        
        report.append(f"{'Table Name':<60} {'Duration(s)':<10} {'Status':<8}")
        report.append("-" * 80)
        
        for table_name, timing in sorted_tables[:20]:
            report.append(f"{table_name:<60} {timing['duration']:<10.3f} {timing['status']:<8}")
    else:
        report.append("No valid table loading time data found")
    
    # Fastest tables
    report.append(f"\n🚀 Fastest Loading Tables (Top 20):")
    report.append("-" * 80)
    
    if valid_tables:
        sorted_tables = sorted(valid_tables.items(), key=lambda x: x[1]['duration'])
        
        report.append(f"{'Table Name':<60} {'Duration(s)':<10} {'Status':<8}")
        report.append("-" * 80)
        
        for table_name, timing in sorted_tables[:20]:
            report.append(f"{table_name:<60} {timing['duration']:<10.3f} {timing['status']:<8}")
    
    # Failed tables
    failed_tables = {name: timing for name, timing in data['table_timings'].items() 
                    if timing['status'] != 'OK' and timing['status'] != 'unknown'}
    
    if failed_tables:
        report.append(f"\n❌ Failed Loading Tables:")
        report.append("-" * 80)
        
        report.append(f"{'Table Name':<60} {'Status':<15} {'Last Event Time'}")
        report.append("-" * 80)
        
        for table_name, timing in failed_tables.items():
            last_time = timing['finish_time'] or timing['execute_time'] or timing['schedule_time']
            report.append(f"{table_name:<60} {timing['status']:<15} {last_time}")
    
    # Startup phase timeline
    report.append(f"\n⏰ Startup Phase Timeline:")
    report.append("-" * 80)
    
    for phase in data['startup_phases']:
        report.append(f"[{phase['time']}] {phase['phase']}: {phase['details']}")
    
    # Error statistics
    if data['errors_by_table']:
        report.append(f"\n🚨 Table-Related Errors ({len(data['errors_by_table'])} tables with errors):")
        report.append("-" * 80)
        
        for table_name, errors in list(data['errors_by_table'].items())[:10]:
            report.append(f"Table: {table_name} ({len(errors)} errors)")
            for error in errors[:2]:  # Show only first 2 errors
                report.append(f"  [{error['time']}] {error['error'][:100]}...")
            report.append("")
    
    report.append("=" * 100)
    
    return "\n".join(report)

def main():
    if len(sys.argv) != 2:
        print("Usage: python table_performance_analysis_EN.py <log_file>")
        sys.exit(1)
    
    log_file = sys.argv[1]
    
    try:
        # Analyze data
        data = analyze_table_performance(log_file)
        
        # Generate report
        report = generate_performance_report(data)
        print(report)
        
        # Save report
        with open('table_performance_report_EN.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Save detailed data
        with open('table_performance_data_EN.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 Performance report saved to: table_performance_report_EN.txt")
        print(f"📊 Detailed data saved to: table_performance_data_EN.json")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
