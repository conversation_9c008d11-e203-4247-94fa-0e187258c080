﻿#if V2
namespace Microsoft.Advertising.Advertiser.Api.V2
#else
namespace Microsoft.Advertising.Advertiser.Api.OData
#endif
{
    using System;
    using System.Collections.Generic;
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.Configuration;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.AdCenter.Deployment.Configurations;

    [TestClass]
    public sealed class ProductionTests : MTTestBase
    {
        const int MaxRetryCount = 10;
        const int RetrySleepInterval = 6;

        private static Dictionary<string, string> DimensionReportColumnMapping = new Dictionary<string, string>();

        static ProductionTests()
        {
            DimensionReportColumnMapping.Add("TimeDate", "GregorianDate,Impressions,Conversions,AdGroupName,AdGroupId,CampaignName,CampaignId,Clicks,CTR,AverageCPC,Spend,AveragePosition,CPA,ConversionRate,RevenueOnAdSpend,AdvertiserReportedRevenue,AuctionWonPercent,AuctionLostToBudgetPercent,AuctionLostToRankPercent,AuctionLostToLandingPercent,AuctionLostToAdQualityPercent,AuctionLostToBidPercent");
            DimensionReportColumnMapping.Add("AgeAndGender", "AgeGroup,Gender,EstimatedImpressions,EstimatedClicks,EstimatedImpressionRate,EstimatedConversions,EstimatedConversionRate,EstimatedCTR,EstimatedClickRate,Medium,AdGroupName,AdGroupId,CampaignName,CampaignId");
            DimensionReportColumnMapping.Add("CallForwardingDetail", "StartTime,EndTime,CallStatus,CallDuration,AreaCode,City,StateOrProvince,AdGroupName,AdGroupId,CampaignName,CampaignId");
            DimensionReportColumnMapping.Add("DestinationUrl", "KeywordDestinationUrl,CampaignName,Clicks,Impressions,CTR,AverageCPC,AveragePosition,ConversionRate,CPA,FinalUrl,UrlCustomParameters,TrackingTemplate,AdGroupName,AdGroupId,CampaignId,Spend,Conversions,RevenueOnAdSpend,AdvertiserReportedRevenue");
            DimensionReportColumnMapping.Add("Geographic", "CountryName,Impressions,TargetedLocationType,StateOrProvince,PostalCode,City,MetroArea,CountyName,TargetLocation,Radius,MostSpecificLocation,AdGroupName,AdGroupId,CampaignName,CampaignId,Clicks,CTR,AverageCPC,Spend,AveragePosition,CPA,Conversions,ConversionRate,RevenueOnAdSpend,AdvertiserReportedRevenue,Downloads,PostClickDownloadRate,CostPerDownload,FirstLaunches,PostClickFirstLaunchRate,CostPerFirstLaunch,Purchases,PostInstallPurchaseRate,CostPerPurchase,Subscriptions,PostInstallSubscriptionRate,CostPerSubscription");
            DimensionReportColumnMapping.Add("PublisherWebsite", "PublisherWebsite,Impressions,Clicks,CTR,AverageCPC,AveragePosition,ConversionRate,CPA,Network,AdGroupName,AdGroupId,CampaignName,CampaignId,Spend,Conversions,RevenueOnAdSpend,AdvertiserReportedRevenue");
            DimensionReportColumnMapping.Add("SearchTerms", "SearchTerm,CampaignType,KeywordName,OrderItemId,AdGroupName,AdGroupId,CampaignName,CampaignId,Clicks,Impressions,CTR,AverageCPC,Spend,AveragePosition,CPA,Conversions,ConversionRate,RevenueOnAdSpend,AdvertiserReportedRevenue");
            DimensionReportColumnMapping.Add("UserLocations", "CountryName,CampaignName,Impressions,City,MetroArea,CountyName,PostalCode,StateOrProvince,TargetLocation,Radius,AdGroupName,AdGroupId,CampaignId,Clicks,CTR,AverageCPC,Spend,AveragePosition,CPA,Conversions,ConversionRate,RevenueOnAdSpend,AdvertiserReportedRevenue,Downloads,PostClickDownloadRate,CostPerDownload,FirstLaunches,PostClickFirstLaunchRate,CostPerFirstLaunch,Purchases,PostInstallPurchaseRate,CostPerPurchase,Subscriptions,PostInstallSubscriptionRate,CostPerSubscription");
            DimensionReportColumnMapping.Add("ShoppingItemId", "CategoryL1,CategoryL2,CategoryL3,CategoryL4,CategoryL5,ProductType1,ProductType2,ProductType3,ProductType4,ProductType5,CustomLabel0,CustomLabel1,CustomLabel2,CustomLabel3,CustomLabel4,Brand,Condition,Title,MerchantIdentifier,ProviderId,Language,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue");
        }

        #region ReportService and Data Service tests
        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        public void GetTimeDateDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "TimeDate";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        [Ignore] //deprecated 
        public void GetAgeAndGenderDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "AgeAndGender";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        [Ignore] //deprecated 
        public void GetCallForwardingDetailDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "CallForwardingDetail";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        public void GetDestinationUrlDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "DestinationUrl";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        public void GetGeographicDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "Geographic";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        [Ignore] //deprecated 
        public void GetPublisherWebsiteDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "PublisherWebsite";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        [Ignore] //deprecated 
        public void GetSearchTermsDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "SearchTerms";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        public void GetUserLocationsDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "UserLocations";
            RunTest((testId) =>
            {
                GetDimensionReport(testId, dimensionReportType);
                GetDimensionReportDownload(testId, dimensionReportType);
            });
        }

        [TestMethod, Priority(2), TestCategory("ReportServiceProductionBVT")]
        [Owner(TestOwners.DimensionTab)]
        public void GetShoppingItemIdDimensionReport()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            string dimensionReportType = "ShoppingItemId";
            RunTest((testId) =>
                    {
                        GetDimensionReport(testId, dimensionReportType);
                        GetDimensionReportDownload(testId, dimensionReportType);
                    });
        }
        #endregion

        #region Private helper methods
        private void GetDimensionReport(string testId, string dimensionReport)
        {
            var columns = DimensionReportColumnMapping[dimensionReport];
            var customerInfo = MTTestDepot.Instance.GetAccountInfo(testId);
            var queryString = GetDimensionReportQueryString(customerInfo, dimensionReport, columns);
            var baseUrl = new AdCenterConfiguration().CampaignManagement.BingAdsODataApi.ServiceUri + "/V2";
            var getUrl = baseUrl + queryString;

            ApiHelper.CallApi(
                customerInfo,
                c => c.GetAsync(getUrl),
                responseMessage =>
                {
                    Assert.IsTrue(responseMessage.IsSuccessStatusCode);
                },
                null,
                true);
        }

        private void GetDimensionReportDownload(string testId, string dimensionReport)
        {
            var columns = DimensionReportColumnMapping[dimensionReport];
            var customerInfo = MTTestDepot.Instance.GetAccountInfo(testId);
            var queryString = $"$select={columns}";

            long executionId;
            var headers = DimensionReportColumnMapping[dimensionReport].Split(',');

            int bidateRangeInDays = -14;
            DateTime biStatsStartDate = DateTime.Now.AddDays(bidateRangeInDays);
            DateTime biStatsEndDate = DateTime.Now;

            var filePath = ReportDataServiceTests.DownloadReport(customerInfo, dimensionReport, "Csv", headers, queryString,
                MaxRetryCount, RetrySleepInterval, TestContext.TestRunDirectory, out executionId, biStatsStartDate, biStatsEndDate, useBigToken:true);

            Assert.AreNotEqual(string.Empty, filePath);
        }

        private string GetDimensionReportQueryString(CustomerInfo customerInfo, string reportType, string columns, int bidateRangeInDays = -14, string orderByColumn = "CampaignName")
        {
            DateTime biStatsStartDate = DateTime.Now.AddDays(bidateRangeInDays);
            DateTime biStatsEndDate = DateTime.Now;
            string entityUrl = $"/Customers({customerInfo.CustomerId})/Accounts({customerInfo.AccountIds[0]})/Default.DimensionReport(Locale = 1, ReportType = Enum.DimensionReportType'{reportType}')";
            string urlParams = $"&startdate={biStatsStartDate.ToString("MM/dd/yy")}&enddate={biStatsEndDate.ToString("MM/dd/yy")}&$select={columns}&$orderby={orderByColumn}&$top=10";
            return entityUrl + "?" + urlParams.Substring(1);
        }
        #endregion
    }
}
