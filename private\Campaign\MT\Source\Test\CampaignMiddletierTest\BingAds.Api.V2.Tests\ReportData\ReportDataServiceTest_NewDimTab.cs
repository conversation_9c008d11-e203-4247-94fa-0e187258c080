﻿namespace Microsoft.Advertising.Advertiser.Api.V2.ReportData
{
    using CampaignMiddleTierTest.Framework;
    using ReportDownload;
    using System.IO;
    using VisualStudio.TestTools.UnitTesting;

    [TestClass]
    public partial class ReportDataServiceTest_NewDimTab : CampaignTestBase
    {
        private CustomerInfo customerInfo;

        private const int numOfAccounts = 2;

        [TestInitialize]
        public void Initialize()
        {
            this.customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                numOfAccounts,
                false);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void TimeDate_Basic()
        {
            ExecuteReportDataDownload(
                "TimeDate",
                new string[] { "GregorianDate", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" },
                "Daily");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void TimeDayOfWeek_Basic()
        {
            ExecuteReportDataDownload(
                "TimeDayOfWeek",
                new string[] { "DayOfWeek", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" },
                "DayOfWeek");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void TimeHourOfDay_Basic()
        {
            ExecuteReportDataDownload(
                "TimeHourOfDay",
                new string[] { "HourOfDay", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" },
                "HourOfDay");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void TimeMonth_Basic()
        {
            ExecuteReportDataDownload(
                "TimeMonth",
                new string[] { "Month", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" },
                "Monthly");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void TimeQuarter_Basic()
        {
            ExecuteReportDataDownload(
                "TimeQuarter",
                new string[] { "Quarter", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" },
                "Quarterly");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void TimeWeek_Basic()
        {
            ExecuteReportDataDownload(
                "TimeWeek",
                new string[] { "Week", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" },
                "Weekly");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void TimeYear_Basic()
        {
            ExecuteReportDataDownload(
                "TimeYear",
                new string[] { "Year", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" },
                "Yearly");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [Ignore] //deprecated ClickhouseQueryBuilder: Query type prc_AgeGenderSummary_Dim not supported.
        public void AgeAndGender_Basic()
        {
            ExecuteReportDataDownload(
                "AgeAndGender",
                new string[] { "AgeGroup", "Gender", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "Conversions" });
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [Ignore] //deprecated 
        public void CallForwardingDetail_Basic()
        {
            ExecuteReportDataDownload(
                "CallForwardingDetail",
                new string[] { "StartTime", "EndTime", "CampaignName", "CallStatus", "CallDuration" });
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void DestinationUrl_Basic()
        {
            ExecuteReportDataDownload(
                "DestinationUrl",
                new string[] { "KeywordDestinationUrl", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [Ignore] //deprecated 
        public void PublisherWebsite_Basic()
        {
            ExecuteReportDataDownload(
                "PublisherWebsite",
                new string[] { "PublisherWebsite", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void Geographic_Basic()
        {
            ExecuteReportDataDownload(
                "Geographic",
                new string[] { "CountryName", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [Ignore] //deprecated 
        public void SearchTerms_Basic()
        {
            ExecuteReportDataDownload(
                "SearchTerms",
                new string[] { "SearchTerm", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ShoppingBrand_Basic()
        {
            ExecuteReportDataDownload(
                "ShoppingBrand",
                new string[] { "Brand", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ShoppingCategory_Basic()
        {
            ExecuteReportDataDownload(
                "ShoppingCategory",
                new string[] { "CategoryL1", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ShoppingItemId_Basic()
        {
            ExecuteReportDataDownload(
                "ShoppingItemId",
                new string[] { "MerchantIdentifier", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ShoppingProductType_Basic()
        {
            ExecuteReportDataDownload(
                "ShoppingProductType",
                new string[] { "ProductType1", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ShoppingStoreId_Basic()
        {
            ExecuteReportDataDownload(
                "ShoppingStoreId",
                new string[] { "ProviderId", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void UserLocations_Basic()
        {
            ExecuteReportDataDownload(
                "UserLocations",
                new string[] { "CountryName", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CPA", "Conversions" });
        }

        public void ExecuteReportDataDownload(string reportType, string[] columns, string reportAggregation = "Summary")
        {
            var filePath = ReportDataDownloadTestHelper.ExecuteReportDataDownload(
                    "Csv",
                    null,
                    null,
                    DateRangePreset.Today,
                    reportType,
                    null,
                    columns,
                    "IncludeColumnHeaders,IncludeReportHeader,IncludeReportFooter",
                    1033,
                    null,
                    reportAggregation,
                    this.customerInfo,
                    TestContext.TestRunDirectory,
                    null,
                    this.customerInfo.AccountIds.ToArray(),
                    taskItemType: TaskItemType.DimensionTabReport,
                    schemaName: "_Default",
                    clientApplicationType: "ReportingUI");
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {filePath} does not have file.");
        }
    }
}