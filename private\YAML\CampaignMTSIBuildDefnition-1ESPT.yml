# This Yaml Document has been converted by ESAI Yaml Pipeline Conversion Tool.
# Please make sure to check all the converted content, it is your team's responsibility to make sure that the pipeline is still valid and functions as expected.
# This pipeline will be extended to the WebXT template
# Applied 1ES migration standard adjustments per migration rules
trigger: none
name: $(Date:yyyyMMdd).$(Rev:r)
variables:
- name: Auth_PAT
  value: utdbvjxuphixrrcbjdvrj2qevnoksa52zsctrou5hskjnw2wrmeq
- name: Auth_User
  value: adlabtfs
- name: ImageTag
  value: ''
- name: RetagFromPreviousImage
  value: ''
- group: batadmin_keyvault
resources:
  repositories:
  - repository: "WebXT.Pipeline.Templates"
    type: git
    name: Engineering Fundamentals/WebXT.Pipeline.Templates
    ref: refs/heads/main
  - repository: AdsAppsYamlTemplate
    type: git
    name: Bing_Ads/AdsApps_Yaml_Template
    ref: refs/heads/master
extends:
  template: v1/<EMAIL>
  parameters:
    pool:
      name: Azure-Pipelines-1ESPT-ExDShared
      image: windows-latest
      os: windows
    sdl:
      sourceAnalysisPool:
        name: Azure-Pipelines-1ESPT-ExDShared
        image: windows-latest
        os: windows
    customBuildTags:
    - ES365AIMigrationTooling
    authenticatedContainerRegistries:
      - serviceConnection: stagingarea.azurecr.io
    stages:
    - stage: stage
      jobs:
      - job: Phase_1
        displayName: Phase 1
        cancelTimeoutInMinutes: 1
        pool:
          name: BingAds-AdsAppsMT-Hosted-EUS
        templateContext:
          outputs:
          - output: pipelineArtifact
            displayName: 'Publish Artifact: BuildDrop'
            targetPath: $(Build.ArtifactStagingDirectory)\BuildDrop
            artifactName: BuildDrop
          - output: pipelineArtifact
            displayName: 'Publish Artifact: DropMetadataCampaignMT'
            targetPath: $(Build.ArtifactStagingDirectory)\CampaignMT
            artifactName: DropMetadataCampaignMT
        steps:
        - checkout: none
          persistCredentials: True
        - task: PowerShell@2
          name: PowerShell3
          displayName: Generate CampaignMT MetaData Drop Json file
          inputs:
            targetType: inline
            script: |-
              $asdirectory = "$(Build.ArtifactStagingDirectory)"
              $buildPath = "$(APAdCoreCampaignMT)"
              Write-Host "Build Path is:$buildPath"
              $subDir=$asdirectory + '\' + "BuildDrop" 
              if(!(Test-Path -Path $subDir)) {mkdir $subDir }
              $DropFile=$subDir + '\drop.txt'
              Write-Host $DropFile
              $buildPath |  Out-File $DropFile
              #Campaign MT
              $buildPath = "$(APAdCoreCampaignMT)".Trim()
              Write-Host "Build Path is:$buildPath"
              $retagFromPreviousImage = [Environment]::GetEnvironmentVariable("RetagFromPreviousImage")
              Write-Host "RetagFromPreviousImage is:$retagFromPreviousImage"
              $VstsDropUrl=@{}
              $VstsDropUrl.VstsDropUrl=$buildPath
              $VstsDropBuildArtifact=@{}
              $VstsDropBuildArtifact.VstsDropBuildArtifact=$VstsDropUrl
              if ($Null -ne $retagFromPreviousImage -and "" -ne $retagFromPreviousImage ) {
                  $VstsDropBuildArtifact.RetagFromPreviousImage=$retagFromPreviousImage
              }
              $subDir=$asdirectory + '\' + "CampaignMT" 
              if(!(Test-Path -Path $subDir)) {mkdir $subDir }
              Write-Host $subDir
              $jsonFile=$subDir + '\VSTSDrop.json'
              Write-Host $jsonFile
              #$jsonFile='\CampaignMTVSTSDrop.json'
              $VstsDropBuildArtifact | ConvertTo-Json | Out-File $jsonFile
        - task: CopyFiles@2
          name: CopyFiles7
          displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
          inputs:
            SourceFolder: $(Build.ArtifactStagingDirectory)
            Contents: '**\*'
            TargetFolder: $(Build.ArtifactStagingDirectory)
      - job: Job_1
        displayName: Build Containers and Push
        dependsOn: Phase_1
        pool:
          name: CampaignMT-ContainerImageBuilders-1ESPT
          os: linux
        templateContext:
          inputs:
          - input: pipelineArtifact
            displayName: 'Download Build Artifacts'
            artifactName: DropMetadataCampaignMT
            targetPath: $(System.ArtifactsDirectory)/CampaignMTSIBuildDefnition/
          outputs:
          - output: pipelineArtifact
            displayName: 'Publish Artifact - Deployment'
            targetPath: $(System.DefaultWorkingDirectory)/containerapps/AnyCPU/Release/Deployment/ContainerApps/Deployment
            artifactName: deployment
        steps:
        - checkout: none
          persistCredentials: True
        - task: Bash@3
          displayName: Initiate docker configuration if not set previously
          inputs:
            targetType: inline
            script: |-
              #!/bin/bash
              echo "current docker system info"
              docker system info
              if [ -f "/etc/docker/daemon.json" ]; then 
                  echo "current docker daemon setting"
                  cat daemon.json
                  exit 0
              fi
              echo "==========================================="
              echo "add customized docker daemon configuration"
              echo '{"data-root": "/mnt/docker-data"}' > daemon.json
              sudo cp daemon.json /etc/docker/daemon.json
              sudo cat /etc/docker/daemon.json
              echo "==========================================="
              echo "restart docker daemon to enable the new configuration"
              sudo systemctl restart docker
              echo "==========================================="
              echo "after customization, current docker system info"
              docker system info
        - task: PowerShell@2
          displayName: Set MTBuild and ImageTag
          inputs:
            targetType: inline
            script: |
              $AdsAppsMTArtifactFolder = "$(System.ArtifactsDirectory)/CampaignMTSIBuildDefnition/"
              $targetFileName = "VSTSDrop.json"
              Write-Host "Looking for $targetFileName in $AdsAppsMTArtifactFolder"
              $foundFile = Get-ChildItem -Path $AdsAppsMTArtifactFolder -Include $targetFileName -Recurse
              Write-Host "Found ${targetFileName}: " + $foundFile.FullName
              $fileContent = Get-Content -Path $foundFile.FullName | ConvertFrom-Json
              $buildDropUrl = $fileContent.VstsDropBuildArtifact.VstsDropUrl
              Write-Host "Got build url $buildDropUrl from $targetFileName"
              Write-Host "Setting variable MTBuild to $buildDropUrl"
              $retagFromPreviousImage = $fileContent.RetagFromPreviousImage
              Write-Host "Got RetagFromImage $retagFromPreviousImage from $targetFileName"
              $mtBuild = $env:MTBuild
              if ($Null -eq $mtBuild -or "" -eq $mtBuild) {
                  Write-Host "##vso[task.setvariable variable=MTBuild]$buildDropUrl"
                  $mtBuild = $buildDropUrl
              } else {
                  $mtBuild = $env:MTBuild
                  Write-Host "MTBuild is already set, will not use artifact build. MTBuild set to $mtBuild"
              }
              Write-Host "Image tag variable from environment: $env:ImageTag"
              Write-Host "Image tag variable from environment:  $(ImageTag)"
              $targetImageTag = "$(ImageTag)"
              if ($Null -eq $targetImageTag -or "" -eq $targetImageTag ) {
                  $components = $mtBuild.Split('/')
                  $hash = $components[-2]
                  $buildId = $components[-1]
                  $shortHash = $hash.SubString(0, 8)
                  Write-Host "Parsed git commit hash $hash , git commit shortHash $shortHash and buildId $buildId from $mtBuild"
                  $targetImageTag = "$env:BUILD_BUILDID-$shortHash-staging"
              }  else {
                  Write-Host "ImageTag is already set to $targetImageTag"
                  Write-Host "Set NewImageTag to null to avoid unexpected retagging"
                  Write-Host "##vso[task.setvariable variable=NewImageTag]''"
              }
              if ($Null -ne $retagFromPreviousImage -and "" -ne $retagFromPreviousImage ) {
                  Write-Host "Will Retag previous image:$retagFromPreviousImage to the target image name $targetImageTag "
                  $newImageTag = $targetImageTag
                  $imageTag = $retagFromPreviousImage
                  Write-Host "Setting ImageTag to $imageTag"
                  Write-Host "Setting NewImageTag to $newImageTag"
                  Write-Host "##vso[task.setvariable variable=ImageTag]$imageTag"
                  Write-Host "##vso[task.setvariable variable=NewImageTag]$newImageTag"    
              } else {
                  Write-Host "Will build new container image with the target image name $targetImageTag"
                  $imageTag = $targetImageTag
                  Write-Host "Setting ImageTag to $targetImageTag"
                  Write-Host "Setting NewImageTag to null to bypass retagging process"
                  Write-Host "##vso[task.setvariable variable=ImageTag]$imageTag"
                  Write-Host "##vso[task.setvariable variable=NewImageTag]''"
              }
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-campaignmt-docker-build-and-push-v7.yml@AdsAppsYamlTemplate
          parameters:
            imageTag: $(ImageTag)
            MTBuild: $(MTBuild)
      - job: Job_2
        displayName: Build FDP Container Image
        dependsOn: Phase_1
        pool:
          name: Azure-Pipelines-1ESPT-ExDShared
          image: ubuntu-22.04
          os: linux
        templateContext:
          outputs:
          - output: pipelineArtifact
            displayName: 'Publish Artifact: K8s yaml'
            targetPath: $(System.DefaultWorkingDirectory)/drop/AnyCPU/Release/Deployment/FDP/Container/yaml/
            artifactName: yaml
          - output: containerImage
            image: stagingarea.azurecr.io/fdp:$(Build.BuildId)
          - output: containerImage
            image: stagingarea.azurecr.io/fdpweb:$(Build.BuildId)
        steps:
        - checkout: none
          persistCredentials: True
        - template: private/TaskGroups/task-group-msasg-Bing_Ads-fdp-build-and-push-v2.yml@AdsAppsYamlTemplate
          parameters:
            APAdCoreCampaignMT: $(APAdCoreCampaignMT)
      - job: Job_3
        displayName: Pull App Config JSON Files
        dependsOn: Phase_1
        pool:
          name: Azure-Pipelines-1ESPT-ExDShared
          image: ubuntu-22.04
          os: linux
        templateContext:
          outputs:
          - output: pipelineArtifact
            displayName: 'Publish Artifact: OMS App Configs'
            targetPath: $(System.DefaultWorkingDirectory)/drop/AnyCPU/Release/Deployment/App/OrderManagementSystem/AppConfigJSON/
            artifactName: oms
        steps:
        - checkout: none
          persistCredentials: True
        - task: NuGetCommand@2
          displayName: Nuget - Download DropNetCore
          inputs:
            command: custom
            arguments: install DropNetCore -Version 1.0.2 -OutputDirectory "$(System.DefaultWorkingDirectory)/dropclient" -Source https://msasg.pkgs.visualstudio.com/_packaging/BingAdsPackages/nuget/v3/index.json
        - task: CmdLine@2
          displayName: Download Build Drop
          inputs:
            script: |-
              build_path=$(APAdCoreCampaignMT)
              echo $build_path
              export VSTS_DROP_PAT=$(System.AccessToken)
              $(System.DefaultWorkingDirectory)/dropclient/DropNetCore.1.0.2/contentFiles/any/any/dropnetcore/linux-x64/dropnetcore get -d $(System.DefaultWorkingDirectory)/drop --recurse infinite -u $build_path -r AnyCPU/Release/Deployment/App/OrderManagementSystem --patAuthEnvVar VSTS_DROP_PAT --localcachepathoverride '$(System.DefaultWorkingDirectory)/dropcache/'
        - task: CopyFiles@2
          displayName: Copy OMS App Config files
          inputs:
            SourceFolder: $(System.DefaultWorkingDirectory)/drop/AnyCPU/Release/Deployment/App/OrderManagementSystem
            Contents: appsettings*.json
            TargetFolder: $(System.DefaultWorkingDirectory)/drop/AnyCPU/Release/Deployment/App/OrderManagementSystem/AppConfigJSON