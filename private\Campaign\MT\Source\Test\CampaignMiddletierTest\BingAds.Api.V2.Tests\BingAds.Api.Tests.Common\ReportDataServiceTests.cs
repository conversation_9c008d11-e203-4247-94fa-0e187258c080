﻿
#if V2
namespace Microsoft.Advertising.Advertiser.Api.V2
#else
namespace Microsoft.Advertising.Advertiser.Api.OData
#endif
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using AdCenter.Advertiser.CampaignManagement.MT.Messages.Aggregator;
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.Clickhouse;
    using CampaignMiddleTierTest.Framework.Configuration;
    using CampaignMiddleTierTest.Framework.Utilities;
    using ReportDownload;
    using VisualStudio.TestTools.UnitTesting;

    [TestClass]
    public partial class ReportDataServiceTests : CampaignTestBase
    {
        private static int numOfCampaigns = 2;
        // BUGBUG: not the real pilot flag value
        public static int NewDimensionTabPilotFlag = 263;

        private CustomerInfo customerInfo;
        private long[] campaignIds;
        private Dictionary<long, List<long>> campaignIdToAdgroupIds;
        private DateTime startDate;
        private DateTime endDate;

        private string entity;
        private string format;
        private string queryString;
        private string[] headers;

        [TestInitialize]
        public void Initialize()
        {

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ReportDownload)]
        [DataRow(new int[] { })]
        
        public void ReportDataService_Basic_CIOnly(int[] pilotFeature)
        {
            InitializeData(pilotFeature: pilotFeature);
            InitializeTask("Csv");

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);

            var headerList = new List<string>();
            headerList.AddRange(headers);
            headers = headerList.ToArray();

            string expectedCampaignName = "CampaignName";
            int expectedClicks = 1;
            int expectedRowsPerCampaign = 2;

            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            Assert.AreEqual(campaignIds.Count() * expectedRowsPerCampaign, csvParser.NormalRowsCount);
            Assert.IsTrue(DateTime.Parse((string)csvParser.GetRow(0).ItemArray[0]) >= startDate && DateTime.Parse((string)csvParser.GetRow(0).ItemArray[0]) <= endDate);
            Assert.IsTrue(csvParser.GetRow(0).ItemArray[1].ToString().StartsWith(expectedCampaignName, StringComparison.CurrentCultureIgnoreCase));

            var dataRows = csvParser.FindFieldForAllRowsStartingWithAndContaining("Campaign", expectedCampaignName, campaignIds[0].ToString());
            Assert.IsTrue(dataRows.Count > 0);
            Assert.AreEqual(expectedClicks, int.Parse((string)dataRows[0].ItemArray[2]));
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [DataRow(new int[] { })]
        
        public void ReportDataService_ExcelLimit_CIOnly(int[] pilotFeature)
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            const DimensionReportType reportType = DimensionReportType.TimeHourOfDay;

            // Adding 2 campaigns, resulting in 2*1*24 = 48 rows. Download should pass.
            InitializeBiData(campaignsCount: 2, adGroupsCount: 1, reportType: reportType, pilotFeature: pilotFeature);

            this.startDate = DateTime.Today.AddYears(-1);
            InitializeTask("Xlsx", reportType);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), String.Format("Downloaded filepath {0} does not have file.", filePath));
            Assert.IsTrue(filePath.ToLower().EndsWith("xlsx"));

            // Generate data to exceed row limit 3*7*24 = 504. Limit should be set to 500 so download will fail.
            InitializeBiData(campaignsCount: 3, adGroupsCount: 7, reportType: reportType, pilotFeature: pilotFeature);

            this.startDate = DateTime.Today.AddYears(-1);
            InitializeTask("Xlsx", reportType);

            long executionId;
            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, out executionId, startDate, endDate, expectFailed: true);
            Assert.AreEqual("", filePath);

            //Get the task so we can verify it's status
            var task = TestTaskItemOperations.GetTaskItemExecutionById(customerInfo, executionId, expand: "TaskItem");
            Assert.AreEqual("Failed", (string)task.Status.Value);
            Assert.IsNotNull(task.ResultId);
            string message = task.ResultId.Value;
            Assert.AreEqual("TooMuchExcelDataToDownload", message);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [DataRow(new int[] { })]
        
        public void ReportDataService_HourOfDay_CIOnly(int[] pilotFeature)
        {
            InitializeBiData(reportType: DimensionReportType.TimeHourOfDay, pilotFeature: pilotFeature);

            //HourOfDay
            this.startDate = DateTime.Today.AddDays(-7);
            InitializeTask("Tsv", DimensionReportType.TimeHourOfDay);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("tsv"));

            var headerList = new List<string>();
            headerList.AddRange(headers);
            headers = headerList.ToArray();

            int expectedRowsPerCampaign = 24;
            var tsvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Tab, expectedHeader: String.Join(BulkCsvParser.Delimiters.Tab.ToString(), headers));
            Assert.IsTrue(((string)tsvParser.GetFirstRow()["CTR"]).Contains("%"));
            Assert.AreEqual(headers.Length, tsvParser.Headers.Count);
            Assert.AreEqual(campaignIds.Count() * expectedRowsPerCampaign, tsvParser.NormalRowsCount);
            foreach (var campaignId in campaignIds)
            {
                Assert.AreEqual(expectedRowsPerCampaign, tsvParser.FindFieldForAllRowsStartingWithAndContaining("Campaign", "CampaignName", campaignId.ToString()).Count);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_DayOfWeek_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.TimeDayOfWeek);

            this.startDate = DateTime.Today.AddDays(-20);
            InitializeTask("Csvzip", DimensionReportType.TimeDayOfWeek);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            Assert.AreEqual(3, csvParser.NormalRowsCount); //Expect 2 rows per campaign (4 total), minus 1 row that gets filtered out due to clicks=1, not > 1

            var campaignId = campaignIds[0];
            var rows = csvParser.FindFieldForAllRowsStartingWithAndContaining("Campaign", "CampaignName", campaignId.ToString());
            Assert.AreEqual(1, rows.Count);
            Assert.AreEqual(DateTime.Today.DayOfWeek.ToString(), (string)rows[0].ItemArray[0]);

            campaignId = campaignIds[1];
            rows = csvParser.FindFieldForAllRowsStartingWithAndContaining("Campaign", "CampaignName", campaignId.ToString());
            Assert.AreEqual(2, rows.Count);
            var outputDays = rows.Select(t => System.Enum.Parse(typeof(DayOfWeek), t.ItemArray[0].ToString())).OrderBy(t => t).ToList();
            var expectedDays = new List<DayOfWeek>() { DateTime.Today.DayOfWeek, DateTime.Today.AddDays(-20).DayOfWeek }.OrderBy(t => t).ToList();
            Assert.AreEqual(expectedDays.Count(), outputDays.Count(), $"Days count doesn;t match for campaignId {campaignId}");
            Assert.AreEqual(expectedDays[0], outputDays[0]);
            Assert.AreEqual(expectedDays[1], outputDays[1]);
        }


        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_WeekStartDate_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.TimeWeek);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.TimeWeek);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 4;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            Assert.AreEqual(campaignIds.Count() * expectedRowsPerCampaign, csvParser.NormalRowsCount);
            foreach (var adGroupId in campaignIdToAdgroupIds.Values.First())
            {
                Assert.AreEqual(expectedRowsPerCampaign, csvParser.FindFieldForAllRows("Ad group", "AdGroupName" + adGroupId).Count);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_MonthStartDate_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.TimeMonth);

            this.startDate = DateTime.Today.AddMonths(-2);
            InitializeTask("Tsv", DimensionReportType.TimeMonth);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("tsv"));

            int expectedRowsPerCampaign = 3;
            var tsvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Tab, expectedHeader: String.Join(BulkCsvParser.Delimiters.Tab.ToString(), headers));
            Assert.AreEqual(headers.Length, tsvParser.Headers.Count);
            Assert.AreEqual(campaignIds.Count() * expectedRowsPerCampaign, tsvParser.NormalRowsCount);
            foreach (var campaignId in campaignIds)
            {
                Assert.AreEqual(expectedRowsPerCampaign, tsvParser.FindFieldForAllRowsStartingWithAndContaining("Campaign", "CampaignName", campaignId.ToString()).Count);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_QuarterStartDate_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.TimeQuarter);

            this.startDate = DateTime.Today.AddMonths(-4);
            InitializeTask("Csvzip", DimensionReportType.TimeQuarter);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), String.Format("Downloaded filepath {0} does not have file.", filePath));
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            var expectedRowsPerCampaign = DateTime.Today.Month % 3 == 1 ? 3 : 2;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            Assert.AreEqual(campaignIds.Count() * expectedRowsPerCampaign, csvParser.NormalRowsCount);
            foreach (var campaignId in campaignIds)
            {
                Assert.AreEqual(expectedRowsPerCampaign, csvParser.FindFieldForAllRows("Campaign ID", $"[{campaignId}]").Count);
            }
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [DataRow(new int[] { })]
        
        public void ReportDataService_YearNum_CIOnly(int[] pilotFeature)
        {
            InitializeBiData(reportType: DimensionReportType.TimeYear, pilotFeature: pilotFeature);

            this.startDate = DateTime.Today.AddYears(-1);
            InitializeTask("Xlsx", DimensionReportType.TimeYear);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), String.Format("Downloaded filepath {0} does not have file.", filePath));
            Assert.IsTrue(filePath.ToLower().EndsWith("xlsx"));
            //All excel file parsing methods I have tried have not worked, so for now this test does not have file parsing
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_AgeAndGender_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            InitializeBiData(reportType: DimensionReportType.AgeAndGender);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.AgeAndGender);
            List<Dictionary<string, Object>> results = InitializeResultSet(DimensionReportType.AgeAndGender);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.AgeAndGender);

            //Test 2: Mandatory columns
            InitializeTask("Csv", DimensionReportType.AgeAndGender, CallType.Mandatory);
            results = InitializeResultSet(DimensionReportType.AgeAndGender, CallType.Mandatory);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));
            ValidateResults(csvParser, results, DimensionReportType.AgeAndGender, CallType.Mandatory);

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);

            //Test 3: All columns
            InitializeTask("Csv", DimensionReportType.AgeAndGender, CallType.All);
            results = InitializeResultSet(DimensionReportType.AgeAndGender, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.AgeAndGender, CallType.All);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_PublisherWebsite_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            InitializeBiData(reportType: DimensionReportType.PublisherWebsite);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.PublisherWebsite);
            List<Dictionary<string, Object>> results = InitializeResultSet(DimensionReportType.PublisherWebsite);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.PublisherWebsite);

            //Test 2: Mandatory columns
            InitializeTask("Csv", DimensionReportType.PublisherWebsite, CallType.Mandatory);
            results = InitializeResultSet(DimensionReportType.PublisherWebsite, CallType.Mandatory);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.PublisherWebsite, CallType.Mandatory);

            //Test 3: All columns
            InitializeTask("Csv", DimensionReportType.PublisherWebsite, CallType.All);
            results = InitializeResultSet(DimensionReportType.PublisherWebsite, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.PublisherWebsite, CallType.All);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [Ignore] //deprecated ClickhouseQueryBuilder: Query type prc_SearchQuerySummary_Dim not supported.
        public void ReportDataService_SearchTerm_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.SearchTerms);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.SearchTerms);
            List<Dictionary<string, Object>> results = InitializeResultSet(DimensionReportType.SearchTerms);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.SearchTerms);

            //Test 2: Mandatory columns
            InitializeTask("Csv", DimensionReportType.SearchTerms, CallType.Mandatory);
            results = InitializeResultSet(DimensionReportType.SearchTerms, CallType.Mandatory);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.SearchTerms, CallType.Mandatory);

            //Test 3: All columns
            InitializeTask("Csv", DimensionReportType.SearchTerms, CallType.All);
            results = InitializeResultSet(DimensionReportType.SearchTerms, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.SearchTerms, CallType.All);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_LandingPage_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.DestinationUrl);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.DestinationUrl);
            List<Dictionary<string, Object>> results = InitializeResultSet(DimensionReportType.DestinationUrl);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.DestinationUrl);

            //Test 2: Mandatory columns
            InitializeTask("Csv", DimensionReportType.DestinationUrl, CallType.Mandatory);
            results = InitializeResultSet(DimensionReportType.DestinationUrl, CallType.Mandatory);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.DestinationUrl, CallType.Mandatory);

            //Test 3: All columns
            InitializeTask("Csv", DimensionReportType.DestinationUrl, CallType.All);
            results = InitializeResultSet(DimensionReportType.DestinationUrl, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.DestinationUrl, CallType.All);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_CallDetails_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline. as no prc_CallDetailsSummary_Dim usage in the prod
                return;
            }
            InitializeBiData(reportType: DimensionReportType.CallForwardingDetail);

            //Test 1: Default/Mandatory columns
            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.CallForwardingDetail);
            List<Dictionary<string, Object>> results = InitializeResultSet(DimensionReportType.CallForwardingDetail);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.CallForwardingDetail);

            //Test 2: All columns
            InitializeTask("Csv", DimensionReportType.CallForwardingDetail, CallType.All);
            results = InitializeResultSet(DimensionReportType.CallForwardingDetail, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.CallForwardingDetail, CallType.All);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_UserLocation_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.UserLocations);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.UserLocations);
            List<Dictionary<string, Object>> results = InitializeResultSet(DimensionReportType.UserLocations);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.UserLocations);

            //Test 2: Mandatory columns
            InitializeTask("Csv", DimensionReportType.UserLocations, CallType.Mandatory);
            results = InitializeResultSet(DimensionReportType.UserLocations, CallType.Mandatory);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.UserLocations, CallType.Mandatory);

            //Test 3: All columns
            InitializeTask("Csv", DimensionReportType.UserLocations, CallType.All);
            results = InitializeResultSet(DimensionReportType.UserLocations, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.UserLocations, CallType.All);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_Geography_CIOnly()
        {
            InitializeBiData(reportType: DimensionReportType.Geographic);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", DimensionReportType.Geographic);
            List<Dictionary<string, Object>> results = InitializeResultSet(DimensionReportType.Geographic);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.Geographic);

            //Test 2: Mandatory columns
            InitializeTask("Csv", DimensionReportType.Geographic, CallType.Mandatory);
            results = InitializeResultSet(DimensionReportType.Geographic, CallType.Mandatory);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.Geographic, CallType.Mandatory);

            //Test 3: All columns
            InitializeTask("Csv", DimensionReportType.Geographic, CallType.All);
            results = InitializeResultSet(DimensionReportType.Geographic, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, DimensionReportType.Geographic, CallType.All);
        }
                
        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_ShoppingBrand_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            ExecuteShoppingDimensionReport(DimensionReportType.ShoppingBrand);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_ShoppingCategory_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            ExecuteShoppingDimensionReport(DimensionReportType.ShoppingCategory);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_ShoppingItemId_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            ExecuteShoppingDimensionReport(DimensionReportType.ShoppingItemId);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_ShoppingProductType_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            ExecuteShoppingDimensionReport(DimensionReportType.ShoppingProductType);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_ShoppingStoreId_CIOnly()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            ExecuteShoppingDimensionReport(DimensionReportType.ShoppingStoreId);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        public void ReportDataService_CampaignAdgroupNameFilter_CIOnly()
        {
            InitializeBiData(2, 2);

            //HourOfDay
            this.startDate = DateTime.Today.AddDays(-7);
            InitializeTask("Tsv", DimensionReportType.TimeHourOfDay);

            var campaignName = this.campaignIdToAdgroupIds.First().Key.ToString();
            var adGroupName = this.campaignIdToAdgroupIds.First().Value.First().ToString();

            var filterString = $"&$filter=(contains(CampaignName,'{campaignName}') and contains(CampaignName,'_QW[ER%TY''UI')  and endswith(AdGroupName,'{adGroupName}') and startswith(CampaignName, 'Campaign') and contains(CampaignName,'zxc') eq false and CampaignName ne 'CampaignName')";

            queryString += filterString;

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("tsv"));

            var headerList = new List<string>();
            headerList.AddRange(headers);
            headers = headerList.ToArray();

            int expectedRowsPerCampaign = 2;
            var tsvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Tab, expectedHeader: String.Join(BulkCsvParser.Delimiters.Tab.ToString(), headers));
            Assert.AreEqual(headers.Length, tsvParser.Headers.Count);
            Assert.AreEqual(expectedRowsPerCampaign, tsvParser.NormalRowsCount);
            Assert.AreEqual(expectedRowsPerCampaign, tsvParser.FindFieldForAllRowsStartingWithAndContaining("Campaign", "CampaignName", campaignName).Count);
            Assert.AreEqual(expectedRowsPerCampaign, tsvParser.FindFieldForAllRowsStartingWith("Ad Group", "AdGroupName" + adGroupName).Count);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ReportDownload)]
        [DataRow(new int[] { })]
        
        public void ReportDataService_ISMetrics_CIOnly(int[] pilotFeature)
        {
            InitializeBiData(1, 2, false, pilotFeature: pilotFeature);

            long adGroup1, adGroup2;
            adGroup1 = campaignIdToAdgroupIds.First().Value.Min();
            adGroup2 = campaignIdToAdgroupIds.First().Value.Max();

            var auctionData = new Dictionary<long, Dictionary<GridColumn, double>>();
            auctionData.Add(adGroup1, new Dictionary<GridColumn, double>()
            {
                { GridColumn.AuctionParticipation, 25000D },
                { GridColumn.ImpressionWonCount, 10000D },
                { GridColumn.AuctionLostToBudgetCount, 1000D },
                { GridColumn.AuctionLostToRankCount, 2000D },
                { GridColumn.AuctionLostToAdQualityCount, 3000D },
                { GridColumn.AuctionLostToLandingURLCount, 4000D },
                { GridColumn.AuctionLostToBidCount, 5000D }
            });

            auctionData.Add(adGroup2, new Dictionary<GridColumn, double>()
            {
                { GridColumn.AuctionParticipation, 10D },
                { GridColumn.ImpressionWonCount, 5D },
                { GridColumn.AuctionLostToBudgetCount, 1D },
                { GridColumn.AuctionLostToRankCount, 1D },
                { GridColumn.AuctionLostToAdQualityCount, 3D },
                { GridColumn.AuctionLostToLandingURLCount, 0D },
                { GridColumn.AuctionLostToBidCount, 0D }
            });

            Logger.Info("Creating data");
            DimensionReportTestHelpers.InsertBiData(customerInfo, campaignIdToAdgroupIds, 3, auctionData);

            //HourOfDay
            this.startDate = DateTime.Today.AddDays(-7);
            InitializeTask("Csv", DimensionReportType.TimeHourOfDay);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            var headerList = new List<string>();
            headerList.AddRange(headers);
            headers = headerList.ToArray();

            int expectedRowsPerCampaign = 2;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            Assert.AreEqual(expectedRowsPerCampaign, csvParser.NormalRowsCount);

            var rows = csvParser.FindFieldForAllRowsStartingWith("Ad group ID", $"[{adGroup1}]");
            DimensionReportTestHelpers.ValidateISMetrics(auctionData[adGroup1], headerList, rows[0], false);
            rows = csvParser.FindFieldForAllRowsStartingWith("Ad group ID", $"[{adGroup2}]");
            DimensionReportTestHelpers.ValidateISMetrics(auctionData[adGroup2], headerList, rows[0], false);

            // Filter on IS metrics which has some value.
            var filterString = "&$filter=(AuctionWonPercent eq 40 and AuctionLostToBudgetPercent eq 4.0 and AuctionLostToRankPercent ne 7.00 and AuctionLostToLandingPercent ge 12.00 and AuctionLostToAdQualityPercent lt 20 and AuctionLostToBidPercent le 40)";
            var filterQueryString  = queryString + filterString;

            filePath = DownloadReport(customerInfo, entity, format, headers, filterQueryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            Assert.AreEqual(expectedRowsPerCampaign, csvParser.NormalRowsCount);

            rows = csvParser.FindFieldForAllRowsStartingWith("Ad group ID", $"[{adGroup1}]");
            DimensionReportTestHelpers.ValidateISMetrics(auctionData[adGroup1], headerList, rows[0], false);

            // Filter on IS metrics which shows up as - in report.
            filterString = "&$filter=(AuctionWonPercent eq 0.0 and AuctionLostToBudgetPercent eq 0.0 and AuctionLostToRankPercent ne 7.00 and AuctionLostToLandingPercent ge -12.00 and AuctionLostToAdQualityPercent lt 20 and AuctionLostToBidPercent le 40)";
            filterQueryString = queryString + filterString;

            filePath = DownloadReport(customerInfo, entity, format, headers, filterQueryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            Assert.AreEqual(expectedRowsPerCampaign, csvParser.NormalRowsCount);

            rows = csvParser.FindFieldForAllRowsStartingWith("Ad group ID", $"[{adGroup2}]");
            DimensionReportTestHelpers.ValidateISMetrics(auctionData[adGroup2], headerList, rows[0], false);
        }

        private void ExecuteShoppingDimensionReport(DimensionReportType reportType)
        {
            InitializeBiData(reportType: reportType);

            this.startDate = DateTime.Today.AddMonths(-1);
            InitializeTask("Csv", reportType);
            List<Dictionary<string, Object>> results = InitializeResultSet(reportType);

            var filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            int expectedRowsPerCampaign = 1;
            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, reportType);

            //Test 2: Mandatory columns
            InitializeTask("Csv", reportType, CallType.Mandatory);
            results = InitializeResultSet(reportType, CallType.Mandatory);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, reportType, CallType.Mandatory);

            //Test 3: All columns
            InitializeTask("Csv", reportType, CallType.All);
            results = InitializeResultSet(reportType, CallType.All);

            filePath = DownloadReport(customerInfo, entity, format, headers, queryString,
                10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {0} does not have file.", filePath);
            Assert.IsTrue(filePath.ToLower().EndsWith("csv"));

            expectedRowsPerCampaign = 1;
            csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma, expectedHeader: String.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));
            Assert.AreEqual(headers.Length, csvParser.Headers.Count);
            ValidateResults(csvParser, results, reportType, CallType.All);
        }

        private void InitializeData(int estimateRows = 3, int[] pilotFeature = null)
        {
            this.customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                3,
                false);
            if (pilotFeature != null && pilotFeature.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId, false, pilotFeature);
            }
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId, false, NewDimensionTabPilotFlag);
            var campaignInfos = DimensionReportTestHelpers.CreateFakeCampaignIdAndAdgroupIds(customerInfo, 2);

            this.startDate = DateTime.Today.AddDays(-31);
            this.endDate = DateTime.Today.AddDays(-1);

            campaignIds = campaignInfos.Keys.OrderBy(x => x).ToArray();
            Logger.Info("Creating data");

            // add bi data
            DimensionReportTestHelpers.InsertTimeReportBiData(customerInfo, campaignIds, estimateRows);
        }

        private void InitializeBiData(int campaignsCount = 2, int adGroupsCount = 1, bool insertData = true, DimensionReportType reportType = DimensionReportType.TimeDate, bool useConversionCredit=false,
            int[] pilotFeature = null)
        {
            this.customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                3,
                false);
            DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId, false, NewDimensionTabPilotFlag);
            if(pilotFeature != null && pilotFeature.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(customerInfo.CustomerId, false, pilotFeature);
            }

            var campaignInfos = DimensionReportTestHelpers.CreateFakeCampaignIdAndAdgroupIds(customerInfo, campaignsCount, adGroupsCount);

            this.endDate = DateTime.Today;

            this.campaignIds = campaignInfos.Keys.OrderBy(x => x).ToArray();

            this.campaignIdToAdgroupIds = new Dictionary<long, List<long>>();
            foreach (var campaignInfo in campaignInfos)
            {
                var adGroupIds = campaignInfo.Value.ToList();
                this.campaignIdToAdgroupIds.Add(campaignInfo.Key, adGroupIds);
            }

            if (insertData)
            {
                Logger.Info("Creating data");
                Tuple<List<BiData>, List<List<int>>> tuple;
                List<BiData> resultData;
                List<List<int>> estimateData;
                // add bi data
                switch (reportType)
                {
                    case DimensionReportType.TimeHourOfDay:
                        InsertBiData_TimeHourOfDayTest(customerInfo, this.campaignIdToAdgroupIds);
                        break;
                    case DimensionReportType.TimeDayOfWeek:
                    case DimensionReportType.TimeDate:
                    case DimensionReportType.TimeWeek:
                    case DimensionReportType.TimeMonth:
                    case DimensionReportType.TimeQuarter:
                    case DimensionReportType.TimeYear:
                        InsertBiData_TimeRangeTest(customerInfo, this.campaignIdToAdgroupIds);
                        break;
                    case DimensionReportType.AgeAndGender:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, DimensionReportType.AgeAndGender);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetAgeGenderSummaryDimensionMockBiData(customerInfo, resultData, false);
                        break;
                    case DimensionReportType.PublisherWebsite:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, DimensionReportType.PublisherWebsite);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetPublisherPlacementSummaryDimensionMockBiData(customerInfo, resultData, false);
                        break;
                    case DimensionReportType.CallForwardingDetail:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, DimensionReportType.CallForwardingDetail);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetCallDetailsSummaryDimensionMockBiData(customerInfo, resultData, false);
                        break;
                    case DimensionReportType.SearchTerms:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, DimensionReportType.SearchTerms, useConversionCredit: useConversionCredit);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetSearchQuerySummaryDimensionMockBiData(customerInfo, resultData, false, useConversionCredit: useConversionCredit);
                        break;
                    case DimensionReportType.DestinationUrl:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, DimensionReportType.DestinationUrl);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetLandingPageSummaryDimensionMockBiData(customerInfo, resultData, false);
                        break;
                    case DimensionReportType.Geographic:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, DimensionReportType.Geographic);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetLocationSummaryDimensionMockBiData(customerInfo, resultData, false);
                        break;
                    case DimensionReportType.UserLocations:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, DimensionReportType.UserLocations);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetLocationSummaryDimensionMockBiData(customerInfo, resultData, false);
                        break;
                    case DimensionReportType.ShoppingBrand:
                    case DimensionReportType.ShoppingCategory:
                    case DimensionReportType.ShoppingItemId:
                    case DimensionReportType.ShoppingProductType:
                    case DimensionReportType.ShoppingStoreId:
                        tuple = InsertBiData(customerInfo, this.campaignIdToAdgroupIds, reportType);
                        resultData = tuple.Item1;
                        estimateData = tuple.Item2;
                        BiDatabaseHelper.SetProductOfferSummaryDimensionMockBiData(customerInfo, resultData, false);
                        break;
                }
            }
        }

        private void InitializeTask(string format = "Csv", DimensionReportType reportType = DimensionReportType.TimeDate, CallType callType = CallType.Default)
        {
            this.entity = reportType.ToString();
            this.format = format;
            InitializeRequestColumns(reportType, callType);
        }

        private void InitializeRequestColumns(DimensionReportType reportType, CallType callType = CallType.Default)
        {
            if (reportType.ToString().Contains("Time"))
            {
                InitializeRequestColumns_Time(reportType);
                return;
            }

            var shoppingAllColumns = new string[]
                {
                    InlineDownloadTestHeaders.CategoryL1,
                    InlineDownloadTestHeaders.CategoryL2,
                    InlineDownloadTestHeaders.CategoryL3,
                    InlineDownloadTestHeaders.CategoryL4,
                    InlineDownloadTestHeaders.CategoryL5,
                    InlineDownloadTestHeaders.ProductType1,
                    InlineDownloadTestHeaders.ProductType2,
                    InlineDownloadTestHeaders.ProductType3,
                    InlineDownloadTestHeaders.ProductType4,
                    InlineDownloadTestHeaders.ProductType5,
                    InlineDownloadTestHeaders.CustomLabel0,
                    InlineDownloadTestHeaders.CustomLabel1,
                    InlineDownloadTestHeaders.CustomLabel2,
                    InlineDownloadTestHeaders.CustomLabel3,
                    InlineDownloadTestHeaders.CustomLabel4,
                    InlineDownloadTestHeaders.Brand,
                    InlineDownloadTestHeaders.Condition,
                    InlineDownloadTestHeaders.Title,
                    InlineDownloadTestHeaders.MerchantIdentifier,
                    InlineDownloadTestHeaders.ProviderId,
                    InlineDownloadTestHeaders.Language,
                    InlineDownloadTestHeaders.CampaignName,
                    InlineDownloadTestHeaders.CampaignId,
                    InlineDownloadTestHeaders.AdGroupName,
                    InlineDownloadTestHeaders.AdGroupId,
                    InlineDownloadTestHeaders.Impressions,
                    InlineDownloadTestHeaders.Clicks,
                    InlineDownloadTestHeaders.CTR,
                    InlineDownloadTestHeaders.AverageCPC,
                    InlineDownloadTestHeaders.TotalEffectiveCost,
                    InlineDownloadTestHeaders.AveragePosition,
                    InlineDownloadTestHeaders.Conversions,
                    InlineDownloadTestHeaders.ConversionRate,
                    InlineDownloadTestHeaders.CPA,
                    InlineDownloadTestHeaders.RevenueOnAdSpend,
                    InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                    InlineDownloadTestHeaders.AllConversions,
                    InlineDownloadTestHeaders.AllConversionCPA,
                    InlineDownloadTestHeaders.AllConversionRate,
                    InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                    InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
                    InlineDownloadTestHeaders.ViewThroughConversions,

                };

            switch (reportType)
            {
                case DimensionReportType.AgeAndGender:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.AgeGroup,
                                InlineDownloadTestHeaders.Gender,
                                InlineDownloadTestHeaders.EstimatedImpressions,
                                InlineDownloadTestHeaders.EstimatedClicks,
                                InlineDownloadTestHeaders.EstimatedCTR
                            };
                            this.queryString = $"$select=AgeGroup,Gender,EstimatedImpressions,EstimatedClicks,EstimatedCTR";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.AgeGroup,
                                InlineDownloadTestHeaders.Gender,
                                InlineDownloadTestHeaders.EstimatedImpressions
                            };
                            this.queryString = $"$select=AgeGroup,Gender,EstimatedImpressions";
                            break;
                        case CallType.All:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.AgeGroup,
                                InlineDownloadTestHeaders.Gender,
                                InlineDownloadTestHeaders.CampaignName,
                                InlineDownloadTestHeaders.CampaignId,
                                InlineDownloadTestHeaders.AdGroupName,
                                InlineDownloadTestHeaders.AdGroupId,
                                InlineDownloadTestHeaders.AdDistribution,
                                InlineDownloadTestHeaders.EstimatedImpressions,
                                InlineDownloadTestHeaders.EstimatedClicks,
                                InlineDownloadTestHeaders.EstimatedCTR
                            };
                            this.queryString = $"$filter=(AgeGroup eq 2 and Gender eq 2 and Medium eq 1 and EstimatedClickRate eq 100 and EstimatedCTR le 50)&$select=AgeGroup,Gender,CampaignName,CampaignId,AdGroupName,AdGroupId,Medium,EstimatedImpressions,EstimatedClicks,EstimatedCTR";
                            break;
                    }
                    break;
                case DimensionReportType.PublisherWebsite:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.PublisherWebsite,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=PublisherWebsite,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.PublisherWebsite,
                                InlineDownloadTestHeaders.Impressions
                            };
                            this.queryString = $"$select=PublisherWebsite,Impressions";
                            break;
                        case CallType.All:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.PublisherWebsite,
                                InlineDownloadTestHeaders.CampaignName,
                                InlineDownloadTestHeaders.CampaignId,
                                InlineDownloadTestHeaders.AdGroupName,
                                InlineDownloadTestHeaders.AdGroupId,
                                InlineDownloadTestHeaders.Network,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition,
                                InlineDownloadTestHeaders.Conversions,
                                InlineDownloadTestHeaders.ConversionRate,
                                InlineDownloadTestHeaders.CPA,
                                InlineDownloadTestHeaders.RevenueOnAdSpend,
                                InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversions,
                                InlineDownloadTestHeaders.AllConversionCPA,
                                InlineDownloadTestHeaders.AllConversionRate,
                                InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
                                InlineDownloadTestHeaders.ViewThroughConversions,
            
                            };
                            this.queryString = $"$filter=(contains(PublisherWebsite, 'campaign0') and Network eq 2)&$select=PublisherWebsite,CampaignName,CampaignId,AdGroupName,AdGroupId,Network,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                            break;
                    }
                    break;
                case DimensionReportType.SearchTerms:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.SearchTerm,
                                InlineDownloadTestHeaders.KeywordDTab,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=SearchTerm,KeywordName,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.SearchTerm,
                                InlineDownloadTestHeaders.Impressions
                            };
                            this.queryString = $"$select=SearchTerm,Impressions";
                            break;
                        case CallType.All:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.SearchTerm,
                                InlineDownloadTestHeaders.CampaignName,
                                InlineDownloadTestHeaders.CampaignId,
                                InlineDownloadTestHeaders.CampaignCampaignType,
                                InlineDownloadTestHeaders.AdGroupName,
                                InlineDownloadTestHeaders.AdGroupId,
                                InlineDownloadTestHeaders.KeywordDTab,
                                InlineDownloadTestHeaders.KeywordId,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition,
                                InlineDownloadTestHeaders.Conversions,
                                InlineDownloadTestHeaders.ConversionRate,
                                InlineDownloadTestHeaders.CPA,
                                InlineDownloadTestHeaders.RevenueOnAdSpend,
                                InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversions,
                                InlineDownloadTestHeaders.AllConversionCPA,
                                InlineDownloadTestHeaders.AllConversionRate,
                                InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend
            
                            };
                            this.queryString = $"$filter=(contains(SearchTerm, 'microsoft') eq false and CampaignCampaignType eq 1 and contains(KeywordName, 'KeywordName') and Impressions gt 2)&$select=SearchTerm,CampaignName,CampaignId,CampaignType,AdGroupName,AdGroupId,KeywordName,OrderItemId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend";
                            break;
                    }
                    break;
                case DimensionReportType.DestinationUrl:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.KeywordDestinationUrl,
                                InlineDownloadTestHeaders.FinalUrls,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=KeywordDestinationUrl,FinalUrl,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.KeywordDestinationUrl,
                                InlineDownloadTestHeaders.Impressions
                            };
                            this.queryString = $"$select=KeywordDestinationUrl,Impressions";
                            break;
                        case CallType.All:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.KeywordDestinationUrl,
                                InlineDownloadTestHeaders.FinalUrls,
                                InlineDownloadTestHeaders.UrlCustomParameters,
                                InlineDownloadTestHeaders.TrackingTemplate,
                                InlineDownloadTestHeaders.CampaignName,
                                InlineDownloadTestHeaders.CampaignId,
                                InlineDownloadTestHeaders.AdGroupName,
                                InlineDownloadTestHeaders.AdGroupId,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition,
                                InlineDownloadTestHeaders.Conversions,
                                InlineDownloadTestHeaders.ConversionRate,
                                InlineDownloadTestHeaders.CPA,
                                InlineDownloadTestHeaders.RevenueOnAdSpend,
                                InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversions,
                                InlineDownloadTestHeaders.AllConversionCPA,
                                InlineDownloadTestHeaders.AllConversionRate,
                                InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
                                InlineDownloadTestHeaders.ViewThroughConversions,
            
                            };
                            this.queryString = $"$filter=(contains(FinalUrl, 'final/url') and endswith(UrlCustomParameters,'/1value') and startswith(TrackingTemplate,'track') and ConversionRate gt 50)&$select=KeywordDestinationUrl,FinalUrl,UrlCustomParameters,TrackingTemplate,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                            break;
                    }
                    break;
                case DimensionReportType.CallForwardingDetail:
                    switch (callType)
                    {
                        //Default and mandatory are same here, so only need 2 cases
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.CallStartTime,
                                InlineDownloadTestHeaders.CallEndTime,
                                InlineDownloadTestHeaders.CallStatus,
                                InlineDownloadTestHeaders.CallDuration
                            };
                            this.queryString = $"$select=StartTime,EndTime,CallStatus,CallDuration";
                            break;
                        case CallType.All:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.CallStartTime,
                                InlineDownloadTestHeaders.CallEndTime,
                                InlineDownloadTestHeaders.CampaignName,
                                InlineDownloadTestHeaders.CampaignId,
                                InlineDownloadTestHeaders.AdGroupName,
                                InlineDownloadTestHeaders.AdGroupId,
                                InlineDownloadTestHeaders.AreaCode,
                                InlineDownloadTestHeaders.City,
                                InlineDownloadTestHeaders.State,
                                InlineDownloadTestHeaders.CallStatus,
                                InlineDownloadTestHeaders.CallDuration
                            };
                            this.queryString = $"$filter=(AreaCode lt 500 and contains(City, 'city') and startswith(StateOrProvince,'Call') and CallDuration gt 15 and CallStatus eq 1)&$select=StartTime,EndTime,CampaignName,CampaignId,AdGroupName,AdGroupId,AreaCode,City,StateOrProvince,CallStatus,CallDuration";
                            break;
                    }
                    break;
                case DimensionReportType.UserLocations:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Country,
                                InlineDownloadTestHeaders.State,
                                InlineDownloadTestHeaders.Metro,
                                InlineDownloadTestHeaders.City,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=CountryName,StateOrProvince,MetroArea,City,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Country,
                                InlineDownloadTestHeaders.Impressions
                            };
                            this.queryString = $"$select=CountryName,Impressions";
                            break;
                        case CallType.All:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Country,
                                InlineDownloadTestHeaders.State,
                                InlineDownloadTestHeaders.Metro,
                                InlineDownloadTestHeaders.City,
                                InlineDownloadTestHeaders.County,
                                InlineDownloadTestHeaders.PostalCode,
                                InlineDownloadTestHeaders.TargetLocation,
                                InlineDownloadTestHeaders.Radius,
                                InlineDownloadTestHeaders.CampaignName,
                                InlineDownloadTestHeaders.CampaignId,
                                InlineDownloadTestHeaders.AdGroupName,
                                InlineDownloadTestHeaders.AdGroupId,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition,
                                InlineDownloadTestHeaders.Conversions,
                                InlineDownloadTestHeaders.ConversionRate,
                                InlineDownloadTestHeaders.CPA,
                                InlineDownloadTestHeaders.RevenueOnAdSpend,
                                InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversions,
                                InlineDownloadTestHeaders.AllConversionCPA,
                                InlineDownloadTestHeaders.AllConversionRate,
                                InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
                                InlineDownloadTestHeaders.ViewThroughConversions,
                                InlineDownloadTestHeaders.Downloads,
                                InlineDownloadTestHeaders.PostClickDownloadRate,
                                InlineDownloadTestHeaders.CostPerDownload,
                                InlineDownloadTestHeaders.FirstLaunches,
                                InlineDownloadTestHeaders.PostClickFirstLaunchRate,
                                InlineDownloadTestHeaders.CostPerFirstLaunch,
                                InlineDownloadTestHeaders.Subscriptions,
                                InlineDownloadTestHeaders.PostInstallSubscriptionRate,
                                InlineDownloadTestHeaders.CostPerSubscription,
                                InlineDownloadTestHeaders.Purchases,
                                InlineDownloadTestHeaders.PostInstallPurchaseRate,
                                InlineDownloadTestHeaders.CostPerPurchase,
            
                            };
                            this.queryString = $"$filter=(CountryName eq 'United States' and StateOrProvince ne 'Ontario' and contains(MetroArea, 'Seattle') and startswith(CountyName,'King') and contains(PostalCode, '103'))&$select=CountryName,StateOrProvince,MetroArea,City,CountyName,PostalCode,TargetLocation,Radius,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions,Downloads,PostClickDownloadRate,CostPerDownload,FirstLaunches,PostClickFirstLaunchRate,CostPerFirstLaunch,Purchases,PostInstallPurchaseRate,CostPerPurchase,Subscriptions,PostInstallSubscriptionRate,CostPerSubscription";
                            break;
                    }
                    break;
                case DimensionReportType.Geographic:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Country,
                                InlineDownloadTestHeaders.State,
                                InlineDownloadTestHeaders.Metro,
                                InlineDownloadTestHeaders.City,
                                InlineDownloadTestHeaders.LocationType,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=CountryName,StateOrProvince,MetroArea,City,TargetedLocationType,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Country,
                                InlineDownloadTestHeaders.Impressions
                            };
                            this.queryString = $"$select=CountryName,Impressions";
                            break;
                        case CallType.All:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Country,
                                InlineDownloadTestHeaders.State,
                                InlineDownloadTestHeaders.Metro,
                                InlineDownloadTestHeaders.City,
                                InlineDownloadTestHeaders.County,
                                InlineDownloadTestHeaders.PostalCode,
                                InlineDownloadTestHeaders.TargetLocation,
                                InlineDownloadTestHeaders.MostSpecificLocation,
                                InlineDownloadTestHeaders.LocationType,
                                InlineDownloadTestHeaders.Radius,
                                InlineDownloadTestHeaders.CampaignName,
                                InlineDownloadTestHeaders.CampaignId,
                                InlineDownloadTestHeaders.AdGroupName,
                                InlineDownloadTestHeaders.AdGroupId,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition,
                                InlineDownloadTestHeaders.Conversions,
                                InlineDownloadTestHeaders.ConversionRate,
                                InlineDownloadTestHeaders.CPA,
                                InlineDownloadTestHeaders.RevenueOnAdSpend,
                                InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversions,
                                InlineDownloadTestHeaders.AllConversionCPA,
                                InlineDownloadTestHeaders.AllConversionRate,
                                InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                                InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
                                InlineDownloadTestHeaders.ViewThroughConversions,
                                InlineDownloadTestHeaders.Downloads,
                                InlineDownloadTestHeaders.PostClickDownloadRate,
                                InlineDownloadTestHeaders.CostPerDownload,
                                InlineDownloadTestHeaders.FirstLaunches,
                                InlineDownloadTestHeaders.PostClickFirstLaunchRate,
                                InlineDownloadTestHeaders.CostPerFirstLaunch,
                                InlineDownloadTestHeaders.Subscriptions,
                                InlineDownloadTestHeaders.PostInstallSubscriptionRate,
                                InlineDownloadTestHeaders.CostPerSubscription,
                                InlineDownloadTestHeaders.Purchases,
                                InlineDownloadTestHeaders.PostInstallPurchaseRate,
                                InlineDownloadTestHeaders.CostPerPurchase,
            
                            };
                            this.queryString = $"$filter=(City eq 'Toronto' and contains(TargetLocation, 'lat') and startswith(MostSpecificLocation,'Tor') and Radius eq 5)&$select=CountryName,StateOrProvince,MetroArea,City,CountyName,PostalCode,TargetLocation,MostSpecificLocation,TargetedLocationType,Radius,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions,Downloads,PostClickDownloadRate,CostPerDownload,FirstLaunches,PostClickFirstLaunchRate,CostPerFirstLaunch,Purchases,PostInstallPurchaseRate,CostPerPurchase,Subscriptions,PostInstallSubscriptionRate,CostPerSubscription";
                            break;
                    }
                    break;

                case DimensionReportType.ShoppingBrand:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Brand,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=Brand,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.Brand,
                                InlineDownloadTestHeaders.Impressions,
                            };
                            this.queryString = $"$select=Brand,Impressions";
                            break;
                        case CallType.All:
                            this.headers = shoppingAllColumns;
                            this.queryString = $"$select=CategoryL1,CategoryL2,CategoryL3,CategoryL4,CategoryL5,ProductType1,ProductType2,ProductType3,ProductType4,ProductType5,CustomLabel0,CustomLabel1,CustomLabel2,CustomLabel3,CustomLabel4,Brand,Condition,Title,MerchantIdentifier,ProviderId,Language,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                            break;
                    }
                    break;

                case DimensionReportType.ShoppingCategory:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.CategoryL1,
                                InlineDownloadTestHeaders.CategoryL2,
                                InlineDownloadTestHeaders.CategoryL3,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=CategoryL1,CategoryL2,CategoryL3,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.CategoryL1,
                                InlineDownloadTestHeaders.Impressions,
                            };
                            this.queryString = $"$select=CategoryL1,Impressions";
                            break;
                        case CallType.All:
                            this.headers = shoppingAllColumns;
                            this.queryString = $"$select=CategoryL1,CategoryL2,CategoryL3,CategoryL4,CategoryL5,ProductType1,ProductType2,ProductType3,ProductType4,ProductType5,CustomLabel0,CustomLabel1,CustomLabel2,CustomLabel3,CustomLabel4,Brand,Condition,Title,MerchantIdentifier,ProviderId,Language,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                            break;
                    }
                    break;
                case DimensionReportType.ShoppingItemId:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.MerchantIdentifier,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=MerchantIdentifier,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.MerchantIdentifier,
                                InlineDownloadTestHeaders.Impressions,
                            };
                            this.queryString = $"$select=MerchantIdentifier,Impressions";
                            break;
                        case CallType.All:
                            this.headers = shoppingAllColumns;
                            this.queryString = $"$select=CategoryL1,CategoryL2,CategoryL3,CategoryL4,CategoryL5,ProductType1,ProductType2,ProductType3,ProductType4,ProductType5,CustomLabel0,CustomLabel1,CustomLabel2,CustomLabel3,CustomLabel4,Brand,Condition,Title,MerchantIdentifier,ProviderId,Language,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                            break;
                    }
                    break;
                case DimensionReportType.ShoppingProductType:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.ProductType1,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=ProductType1,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.ProductType1,
                                InlineDownloadTestHeaders.Impressions,
                            };
                            this.queryString = $"$select=ProductType1,Impressions";
                            break;
                        case CallType.All:
                            this.headers = shoppingAllColumns;
                            this.queryString = $"$select=CategoryL1,CategoryL2,CategoryL3,CategoryL4,CategoryL5,ProductType1,ProductType2,ProductType3,ProductType4,ProductType5,CustomLabel0,CustomLabel1,CustomLabel2,CustomLabel3,CustomLabel4,Brand,Condition,Title,MerchantIdentifier,ProviderId,Language,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                            break;
                    }
                    break;
                case DimensionReportType.ShoppingStoreId:
                    switch (callType)
                    {
                        case CallType.Default:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.ProviderId,
                                InlineDownloadTestHeaders.Impressions,
                                InlineDownloadTestHeaders.Clicks,
                                InlineDownloadTestHeaders.CTR,
                                InlineDownloadTestHeaders.AverageCPC,
                                InlineDownloadTestHeaders.TotalEffectiveCost,
                                InlineDownloadTestHeaders.AveragePosition
                            };
                            this.queryString = $"$select=ProviderId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition";
                            break;
                        case CallType.Mandatory:
                            this.headers = new string[]
                            {
                                InlineDownloadTestHeaders.ProviderId,
                                InlineDownloadTestHeaders.Impressions,
                            };
                            this.queryString = $"$select=ProviderId,Impressions";
                            break;
                        case CallType.All:
                            this.headers = shoppingAllColumns;
                            this.queryString = $"$select=CategoryL1,CategoryL2,CategoryL3,CategoryL4,CategoryL5,ProductType1,ProductType2,ProductType3,ProductType4,ProductType5,CustomLabel0,CustomLabel1,CustomLabel2,CustomLabel3,CustomLabel4,Brand,Condition,Title,MerchantIdentifier,ProviderId,Language,CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                            break;
                    }
                    break;
            }
        }

        private void InitializeRequestColumns_Time(DimensionReportType reportType)
        {
            string dateGrain = "";
            switch (reportType)
            {
                case DimensionReportType.TimeDate:
                    dateGrain = InlineDownloadTestHeaders.TimeDate;
                    this.headers = new string[]
                    {
                        dateGrain,
                        InlineDownloadTestHeaders.CampaignName,
                        InlineDownloadTestHeaders.Clicks,
                    };
                    dateGrain = "GregorianDate"; //Convert "Date" to "GregorianDate" for call
                    this.queryString = $"$select={dateGrain},CampaignName,Clicks";
                    break;
                case DimensionReportType.TimeHourOfDay:
                    dateGrain = InlineDownloadTestHeaders.TimeHourOfDay;
                    this.headers = new string[]
                    {
                        dateGrain,
                        InlineDownloadTestHeaders.CampaignName,
                        InlineDownloadTestHeaders.CampaignId,
                        InlineDownloadTestHeaders.AdGroupName,
                        InlineDownloadTestHeaders.AdGroupId,
                        InlineDownloadTestHeaders.Impressions,
                        InlineDownloadTestHeaders.Clicks,
                        InlineDownloadTestHeaders.CTR,
                        InlineDownloadTestHeaders.AverageCPC,
                        InlineDownloadTestHeaders.ClickAndPhoneSpent,
                        InlineDownloadTestHeaders.AveragePosition,
                        InlineDownloadTestHeaders.Conversions,
                        InlineDownloadTestHeaders.ConversionRate,
                        InlineDownloadTestHeaders.CPA,
                        InlineDownloadTestHeaders.RevenueOnAdSpend,
                        InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                        InlineDownloadTestHeaders.AuctionWonPercent,
                        InlineDownloadTestHeaders.AuctionLostToBudgetPercent,
                        InlineDownloadTestHeaders.AuctionLostToRankPercent,
                        InlineDownloadTestHeaders.AuctionLostToRankAggPercent,
                        InlineDownloadTestHeaders.AbsoluteTopImpressionSharePercent,
                        InlineDownloadTestHeaders.TopImpressionSharePercent,
                        InlineDownloadTestHeaders.AbsoluteTopImpressionRatePercent,
                        InlineDownloadTestHeaders.TopImpressionRatePercent,
                        InlineDownloadTestHeaders.TopImpressionShareLostToRankPercent,
                        InlineDownloadTestHeaders.TopImpressionShareLostToBudgetPercent,
                        InlineDownloadTestHeaders.AbsoluteTopImpressionShareLostToRankPercent,
                        InlineDownloadTestHeaders.AbsoluteTopImpressionShareLostToBudgetPercent,
                        InlineDownloadTestHeaders.AuctionLostToLandingPercent,
                        InlineDownloadTestHeaders.AuctionLostToAdQualityPercent,
                        InlineDownloadTestHeaders.AuctionLostToBidPercent,
                        InlineDownloadTestHeaders.AllConversions,
                        InlineDownloadTestHeaders.AllConversionCPA,
                        InlineDownloadTestHeaders.AllConversionRate,
                        InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                        InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
                        InlineDownloadTestHeaders.ViewThroughConversions,
    
                    };
                    this.queryString = $"$select={dateGrain},CampaignName,CampaignId,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AverageCPC,Spend,AveragePosition,Conversions,ConversionRate,CPA,RevenueOnAdSpend,AdvertiserReportedRevenue,AuctionWonPercent,AuctionLostToBudgetPercent,AuctionLostToRankPercent,AuctionLostToRankAggPercent,AbsoluteTopImpressionSharePercent,TopImpressionSharePercent,AbsoluteTopImpressionRatePercent,TopImpressionRatePercent,TopImpressionShareLostToRankPercent,TopImpressionShareLostToBudgetPercent,AbsoluteTopImpressionShareLostToRankPercent,AbsoluteTopImpressionShareLostToBudgetPercent,AuctionLostToLandingPercent,AuctionLostToAdQualityPercent,AuctionLostToBidPercent,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions";
                    break;
                case DimensionReportType.TimeDayOfWeek:
                    dateGrain = InlineDownloadTestHeaders.TimeDayOfWeek;
                    this.headers = new string[]
                    {
                        dateGrain,
                        InlineDownloadTestHeaders.CampaignName,
                        InlineDownloadTestHeaders.AdGroupName,
                        InlineDownloadTestHeaders.AdGroupId,
                        InlineDownloadTestHeaders.Impressions,
                        InlineDownloadTestHeaders.CTR,
                        InlineDownloadTestHeaders.AverageCPC,
                        InlineDownloadTestHeaders.AveragePosition,
                        InlineDownloadTestHeaders.Conversions,
                        InlineDownloadTestHeaders.CPA,
                        InlineDownloadTestHeaders.AuctionWonPercent,
                        InlineDownloadTestHeaders.AuctionLostToRankPercent,
                        InlineDownloadTestHeaders.AuctionLostToRankAggPercent,
                        InlineDownloadTestHeaders.AuctionLostToAdQualityPercent,
                        InlineDownloadTestHeaders.AllConversions,
                        InlineDownloadTestHeaders.AllConversionCPA,
                        InlineDownloadTestHeaders.AllConversionRate,
                        InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                        InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
                        InlineDownloadTestHeaders.ViewThroughConversions,

                    };
                    this.queryString = $"$select={dateGrain},CampaignName,AdGroupName,AdGroupId,Impressions,CTR,AverageCPC,AveragePosition,Conversions,CPA,AuctionWonPercent,AuctionLostToRankPercent,AuctionLostToRankAggPercent,AuctionLostToAdQualityPercent,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend,ViewThroughConversions&$filter=contains(AdGroupName, 'AdGroupName') and Clicks gt 1";
                    break;
                case DimensionReportType.TimeWeek:
                    dateGrain = InlineDownloadTestHeaders.TimeWeek;
                    this.headers = new string[]
                    {
                        dateGrain,
                        InlineDownloadTestHeaders.AdGroupName,
                        InlineDownloadTestHeaders.Impressions,
                        InlineDownloadTestHeaders.Clicks,
                        InlineDownloadTestHeaders.Conversions
                    };
                    this.queryString = $"$select={dateGrain},AdGroupName,Impressions,Clicks,Conversions";
                    break;
                case DimensionReportType.TimeMonth:
                    dateGrain = InlineDownloadTestHeaders.TimeMonth;
                    this.headers = new string[]
                    {
                        dateGrain,
                        InlineDownloadTestHeaders.CampaignName,
                        InlineDownloadTestHeaders.AdGroupName,
                        InlineDownloadTestHeaders.AdGroupId,
                        InlineDownloadTestHeaders.Impressions,
                        InlineDownloadTestHeaders.Clicks,
                        InlineDownloadTestHeaders.CTR,
                        InlineDownloadTestHeaders.AveragePosition,
                        InlineDownloadTestHeaders.Conversions,
                        InlineDownloadTestHeaders.CPA,
                        InlineDownloadTestHeaders.AuctionWonPercent,
                        InlineDownloadTestHeaders.AllConversions,
                        InlineDownloadTestHeaders.AllConversionCPA,
                        InlineDownloadTestHeaders.AllConversionRate,
                        InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
                        InlineDownloadTestHeaders.AllConversionRevenueOnAdSpend,
    
                    };
                    this.queryString = $"$select={dateGrain},CampaignName,AdGroupName,AdGroupId,Impressions,Clicks,CTR,AveragePosition,Conversions,CPA,AuctionWonPercent,AllConversions,AllConversionCPA,AllConversionRate,AllConversionAdvertiserReportedRevenue,AllConversionRevenueOnAdSpend&$filter=AveragePosition ge 0 and AuctionWonPercent ge 0";
                    break;
                case DimensionReportType.TimeQuarter:
                    dateGrain = InlineDownloadTestHeaders.TimeQuarter;
                    this.headers = new string[]
                    {
                        dateGrain,
                        InlineDownloadTestHeaders.CampaignId,
                        InlineDownloadTestHeaders.AdGroupId,
                        InlineDownloadTestHeaders.Impressions,
                        InlineDownloadTestHeaders.Clicks,
                        InlineDownloadTestHeaders.Conversions
                    };
                    this.queryString = $"$select={dateGrain},CampaignId,AdGroupId,Impressions,Clicks,Conversions";
                    break;
                case DimensionReportType.TimeYear:
                    dateGrain = InlineDownloadTestHeaders.TimeYear;
                    this.headers = new string[]
                    {
                        dateGrain,
                        InlineDownloadTestHeaders.CampaignName,
                        InlineDownloadTestHeaders.Clicks,
                    };
                    this.queryString = $"$select={dateGrain},CampaignName,Clicks";
                    break;
            }
        }


        public static string DownloadReport(
            CustomerInfo cInfo,
            string entity,
            string format,
            string[] headers,
            string queryString,
            int maxRetry,
            int retryIntervalInSec,
            string testDir,
            DateTime? startDate = null,
            DateTime? endDate = null,
            AssociationType? associateType = null,
            AdExtensionsTypeFilter? adExtensionTypeFilter = null,
            KeywordIdAndMatchType[] searchTermKeywords = null,
            long? campaignId = null,
            DateRangePreset? dateRangePreset = null,
            EmailNotificationDeliveryOption emailNotificationDeliveryOption = null,
            bool saveReport = false,
            DateTime? popStartDate = null,
            DateTime? popEndDate = null,
            string PeriodComparisonDateRangePreset = null,
            DimensionReportType? dimensionReportType = null,
            string[] segmentationType = null,
            long? budgetId = null,
            long? adgroupId = null,
            IEnumerable<dynamic> customColumnDefinitions = null)
        {
            // Sleep for 10 seconds in SI to let data replicate from primary DBs to secondary DBs.
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                Thread.Sleep(10 * 1000);
            }

            return ReportDownloadTestHelper.ExecuteReportDownload(cInfo, entity, format, headers, queryString, maxRetry,
                retryIntervalInSec, testDir, startDate, endDate, associateType, adExtensionTypeFilter,
                searchTermKeywords, campaignId, dateRangePreset, emailNotificationDeliveryOption, saveReport,
                popStartDate, popEndDate, PeriodComparisonDateRangePreset, dimensionReportType,
                segmentationType, budgetId, adgroupId, customColumnDefinitions) ;
        }

        public static string DownloadReport(
            CustomerInfo cInfo,
            string entity,
            string format,
            string[] headers,
            string queryString,
            int maxRetry,
            int retryIntervalInSec,
            string testDir,
            out long executionId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            AssociationType? associateType = null,
            AdExtensionsTypeFilter? adExtensionTypeFilter = null,
            KeywordIdAndMatchType[] searchTermKeywords = null,
            long? campaignId = null,
            DateRangePreset? dateRangePreset = null,
            EmailNotificationDeliveryOption emailNotificationDeliveryOption = null,
            bool saveReport = false,
            DateTime? popStartDate = null,
            DateTime? popEndDate = null,
            string PeriodComparisonDateRangePreset = null,
            DimensionReportType? dimensionReportType = null,
            string[] segmentationType = null,
            long? budgetId = null,
            long? adgroupId = null,
            bool expectFailed = false,
            IEnumerable<dynamic> customColumnDefinitions = null,
            bool useBigToken = false)
        {
            // Sleep for 10 seconds in SI to let data replicate from primary DBs to secondary DBs.
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                Thread.Sleep(10 * 1000);
            }

            return ReportDownloadTestHelper.ExecuteReportDownload(cInfo, entity, format, headers, queryString, maxRetry,
                retryIntervalInSec, testDir, out executionId, startDate, endDate, associateType, adExtensionTypeFilter,
                searchTermKeywords, campaignId, dateRangePreset, emailNotificationDeliveryOption, saveReport, 
                popStartDate, popEndDate, PeriodComparisonDateRangePreset, dimensionReportType,
                segmentationType, budgetId, adgroupId, expectFailed, customColumnDefinitions, useBigToken: useBigToken);
        }

        internal static Tuple<List<BiData>, List<List<int>>> InsertBiData(CustomerInfo cInfo,
            Dictionary<long, List<long>> campaignsToAdgroupsDictionary, DimensionReportType reportType, bool useConversionCredit=false)
        {
            List<BiData> resultData = new List<BiData>();
            List<List<int>> estimateData = new List<List<int>>();

            estimateData.Add(
                new List<int>(new int[]
                {
                    cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).ToDateKey(), 5, 15,
                    25, 30, 50, 80
                }));

            int campaignOrdinal = 0;
            foreach (long campaignId in campaignsToAdgroupsDictionary.Keys.OrderBy(t => t))
            {
                foreach (long adGroupId in campaignsToAdgroupsDictionary[campaignId].OrderBy(t => t))
                {
                    int campaignMultiplier = campaignOrdinal + 1;

                    BiData data = new BiData()
                    {
                        Date = DateTime.Today.AddDays(-2),
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        OrderItemId = adGroupId * 100 + campaignOrdinal,
                        AdId = adGroupId * 10 + campaignOrdinal,
                        CallId = adGroupId * 100 + campaignOrdinal,
                        AgeBucketId = (campaignOrdinal % 4) + 1,
                        GenderId = (campaignOrdinal % 2) + 1,
                        NetworkId = 2,
                        Radius = 5,
                        CallStatus = 0,
                        AreaCode = 123,
                        Country = (campaignOrdinal % 2) == 0 ? "United States" : "Canada",
                        State = reportType == DimensionReportType.CallForwardingDetail ? "Call State" : (campaignOrdinal % 2) == 0 ? "Washington" : "Ontario",
                        Metro = (campaignOrdinal % 2) == 0 ? "Seattle" : "",
                        City = reportType == DimensionReportType.CallForwardingDetail ? "Call City" : (campaignOrdinal % 2) == 0 ? "Seattle" : "Toronto",
                        County = (campaignOrdinal % 2) == 0 ? "King" : "",
                        PostalCode = (campaignOrdinal % 2) == 0 ? "98103" : "",
                        TargetedLocation = (campaignOrdinal % 2) == 0 ? "" : "lat 50 lon 60",
                        SearchTerm = $"SearchTerm{campaignOrdinal}",
                        PublisherWebsite = $"http://www.bing.com/campaign{campaignOrdinal}",
                        DestinationUrl = (campaignOrdinal % 2) == 0 ? $"https://www.bing.com/destination/url/ordinal_{campaignOrdinal}" : $"",
                        FinalUrl = (campaignOrdinal % 2) == 0 ? $"" : $"https://www.bing.com/final/url/ordinal_{campaignOrdinal}",
                        CustomParameters = $"customparam/{campaignOrdinal}",
                        TrackingTemplate = $"trackingtemplate/{campaignOrdinal}",
                        CallStartTime = DateTime.Today.AddDays(-2).AddHours(-(1 + campaignOrdinal)),
                        CallEndTime = DateTime.Today.AddDays(-2).AddMinutes(-30),
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                        AllConversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        AllConversionAdvertiserReportedRevenue = campaignMultiplier * GetDimensionReports.costFactor,
                        Downloads = campaignMultiplier * GetDimensionReports.downloadsFactor,
                        FirstLaunches = campaignMultiplier * GetDimensionReports.firstLaunchesFactor,
                        Subscriptions = campaignMultiplier * GetDimensionReports.subscriptionsFactor,
                        Purchases = campaignMultiplier * GetDimensionReports.purchasesFactor,

                        LanguageCode = "EN",
                        CountryCode = AdCenter.Advertiser.CampaignManagement.MT.Entities.Travel.BidMultilplier.CountryCode.US,
                        CustomLabel0 = $"CustomLabel0 {campaignOrdinal}",
                        CustomLabel1 = $"CustomLabel1 {campaignOrdinal}",
                        CustomLabel2 = $"CustomLabel2 {campaignOrdinal}",
                        CustomLabel3 = $"CustomLabel3 {campaignOrdinal}",
                        CustomLabel4 = $"CustomLabel4 {campaignOrdinal}",
                        ProductTypeL1 = $"ProductTypeL1 {campaignOrdinal}",
                        ProductTypeL2 = $"ProductTypeL2 {campaignOrdinal}",
                        ProductTypeL3 = $"ProductTypeL3 {campaignOrdinal}",
                        ProductTypeL4 = $"ProductTypeL4 {campaignOrdinal}",
                        ProductTypeL5 = $"ProductTypeL5 {campaignOrdinal}",
                        OfferCategoryL1 = $"OfferCategoryL1 {campaignOrdinal}",
                        OfferCategoryL2 = $"OfferCategoryL2 {campaignOrdinal}",
                        OfferCategoryL3 = $"OfferCategoryL3 {campaignOrdinal}",
                        OfferCategoryL4 = $"OfferCategoryL4 {campaignOrdinal}",
                        OfferCategoryL5 = $"OfferCategoryL5 {campaignOrdinal}",
                        MerchantProductId = $"MerchantProductId {campaignOrdinal}",
                        Title = $"Title {campaignOrdinal}",
                        Brand = $"Brand {campaignOrdinal}",
                        Condition = "New",
                        ProviderId = (long)cInfo.AccountIds[0] * 10,
                        ProductOfferId = adGroupId * 100 + campaignOrdinal,

                    };
                    if(useConversionCredit)
                    {
                        Random rand = new Random();
                        var allConversionExpectedVal = (float)(rand.Next(100) + rand.NextDouble());
                        data.TotalConversionsCredit = allConversionExpectedVal;
                        data.FullClickConversionsCredit = allConversionExpectedVal;
                        data.ConversionsCredit = (float)(rand.Next(100) + rand.NextDouble());
                        data.ViewThroughConversionsCredit = (float)(rand.Next(100) + rand.NextDouble());                        
                    }
                    resultData.Add(data);
                    campaignOrdinal++;
                }
            }

            return Tuple.Create(resultData, estimateData);
        }

        internal static void InsertBiData_TimeRangeTest(CustomerInfo cInfo, Dictionary<long, List<long>> campaignsToAdgroupsDictionary)
        {
            //Procedure will be used to populate data one time for a test that tests all possible time grains
            List<BiData> resultData = new List<BiData>();
            List<List<int>> estimateData = new List<List<int>>();

            //Populate estimate data (since CampaignId is not passed here, we only want 1 entry per date); HourOfDay does not get row estimates so no entry for it
            if (DateTime.Today.Day < 20)
            {
                //DayOfWeek is populating 21 days back, so this will combine with Week population (aka previous month)
                estimateData.Add(
                    new List<int>(new int[]
                    {
                        cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).ToDateKey(), 0, 0, 0, 0, 0, 0,
                        0
                    }));
                estimateData.Add(
                    new List<int>(new int[]
                    {
                        cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).AddMonths(-1).ToDateKey(), 3,
                        12, 0, 0, 0, 5, 20
                    }));
            }
            else
            {
                //DayOfWeek population will be separate from week population
                estimateData.Add(
                    new List<int>(new int[]
                    {
                        cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).ToDateKey(), 3, 0, 0, 0, 0, 5,
                        20
                    }));
                estimateData.Add(
                    new List<int>(new int[]
                    {
                        cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).AddMonths(-1).ToDateKey(), 0,
                        12, 0, 0, 0, 0, 0
                    }));
            }
            estimateData.Add(
                new List<int>(new int[]
                {
                    cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).AddMonths(-2).ToDateKey(), 0, 0,
                    25, 0, 0, 0, 0
                }));
            estimateData.Add(
                new List<int>(new int[]
                {
                    cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).AddMonths(-4).ToDateKey(), 0, 0, 0,
                    30, 0, 0, 0
                }));
            estimateData.Add(
                new List<int>(new int[]
                {
                    cInfo.AccountIds[0], DateTime.Today.AddDays(-1*DateTime.Now.Day + 1).AddYears(-1).ToDateKey(), 0, 0, 0,
                    0, 41, 0, 0
                }));

            int campaignOrdinal = 0;
            foreach (long campaignId in campaignsToAdgroupsDictionary.Keys.OrderBy(t => t))
            {
                foreach (long adGroupId in campaignsToAdgroupsDictionary[campaignId].OrderBy(t => t))
                {
                    int campaignMultiplier = campaignOrdinal + 1;

                    BiData data = new BiData()
                    {
                        Date = DateTime.Today,
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                    };
                    resultData.Add(data);

                    //HourOfDay
                    data = new BiData()
                    {
                        Date = DateTime.Today.AddDays(-7),
                        HourNum = 10,
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                    };
                    resultData.Add(data);

                    //DayOfWeek
                    data = new BiData()
                    {
                        Date = DateTime.Today.AddDays(-20),
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                    };
                    resultData.Add(data);

                    //Week
                    data = new BiData()
                    {
                        Date = DateTime.Today.AddMonths(-1),
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                    };
                    resultData.Add(data);

                    //Month
                    data = new BiData()
                    {
                        Date = DateTime.Today.AddMonths(-2),
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                    };
                    resultData.Add(data);

                    //Quarter
                    data = new BiData()
                    {
                        Date = DateTime.Today.AddMonths(-4),
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                    };
                    resultData.Add(data);

                    //Year
                    data = new BiData()
                    {
                        Date = DateTime.Today.AddYears(-1),
                        CampaignId = campaignId,
                        OrderId = adGroupId,
                        Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                        Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                        Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                        Spent = campaignMultiplier * GetDimensionReports.costFactor,
                        RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                    };
                    resultData.Add(data);
                    campaignOrdinal++;
                }
            }

            BiDatabaseHelper.SetTimeSummaryDimensionMockBiData(cInfo, resultData, false);
            BiDatabaseHelper.SetEstimatedRowsDimensionMockBiData(cInfo, estimateData);
        }

        /// <summary>
        /// Populates 24 hours worth of BI data for each ad group, used when executing TimeHourOfDay report.
        /// </summary>
        /// <param name="cInfo">Customer info.</param>
        /// <param name="campaignsToAdgroupsDictionary">Set of ad groups.</param>
        internal static void InsertBiData_TimeHourOfDayTest(CustomerInfo cInfo, Dictionary<long, List<long>> campaignsToAdgroupsDictionary)
        {
            List<BiData> resultData = new List<BiData>();

            int campaignOrdinal = 0;
            foreach (long campaignId in campaignsToAdgroupsDictionary.Keys.OrderBy(t => t))
            {
                foreach (long adGroupId in campaignsToAdgroupsDictionary[campaignId].OrderBy(t => t))
                {
                    int campaignMultiplier = campaignOrdinal + 1;

                    for (byte i = 0; i < 24; i += 1)
                    {
                        //HourOfDay
                        BiData data = new BiData()
                        {
                            Date = DateTime.Today.AddDays(-7),
                            HourNum = i,
                            CampaignId = campaignId,
                            OrderId = adGroupId,
                            Clicks = campaignMultiplier * GetDimensionReports.clickFactor,
                            Impressions = campaignMultiplier * GetDimensionReports.impressionFactor,
                            Conversions = campaignMultiplier * GetDimensionReports.conversionsFactor,
                            Spent = campaignMultiplier * GetDimensionReports.costFactor,
                            RevenueOnAdSpend = campaignMultiplier * GetDimensionReports.costFactor,
                        };
                        resultData.Add(data);
                    }

                    campaignOrdinal++;
                }
            }

            Logger.Info($"InsertBiData_TimeHourOfDayTest: Inserting {resultData.Count} rows of data.");
            BiDatabaseHelper.SetTimeSummaryDimensionMockBiData(cInfo, resultData, false);
        }


        internal List<Dictionary<string, Object>> InitializeResultSet(DimensionReportType reportType, CallType callType = CallType.Default)
        {
            //Note: we cannot validate Campaign Name since it is being generated with a random number. However, all other values can be validated
            //Note: ignoring ad group name for now (will require complex logic)
            List<Dictionary<string, Object>> results = new List<Dictionary<string, Object>>();
            switch (reportType)
            {
                case DimensionReportType.AgeAndGender:
                    if (callType == CallType.All)
                    {
                        //Only 1 row returned here
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.AgeGroup, "13-17");
                        rowResults.Add(InlineDownloadTestHeaders.Gender, "Female");
                        rowResults.Add(InlineDownloadTestHeaders.AdDistribution, "Search");
                        results.Add(rowResults);
                    }
                    else
                    {
                        //2 rows returned for Default and Mandatory
                        for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                        {
                            Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                            string age = "";
                            string gender = "";
                            switch (numRowsReturned % 4)
                            {
                                case 0:
                                    rowResults.Add(InlineDownloadTestHeaders.AgeGroup, "0-12");
                                    rowResults.Add(InlineDownloadTestHeaders.Gender, "Male");
                                    break;
                                case 1:
                                    rowResults.Add(InlineDownloadTestHeaders.AgeGroup, "13-17");
                                    rowResults.Add(InlineDownloadTestHeaders.Gender, "Female");
                                    break;
                                case 2:
                                    rowResults.Add(InlineDownloadTestHeaders.AgeGroup, "18-24");
                                    rowResults.Add(InlineDownloadTestHeaders.Gender, "Male");
                                    break;
                                case 3:
                                    rowResults.Add(InlineDownloadTestHeaders.AgeGroup, "25-34");
                                    rowResults.Add(InlineDownloadTestHeaders.Gender, "Female");
                                    break;
                            }
                            results.Add(rowResults);
                        }
                    }
                    break;
                case DimensionReportType.PublisherWebsite:
                    if (callType == CallType.All)
                    {
                        //Only 1 row returned here
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.PublisherWebsite, "http://www.bing.com/campaign0");
                        rowResults.Add(InlineDownloadTestHeaders.Network, "Syndicated search partners");
                        results.Add(rowResults);
                    }
                    else
                    {
                        //2 rows returned for Default and Mandatory
                        for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                        {
                            Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                            rowResults.Add(InlineDownloadTestHeaders.PublisherWebsite, $"http://www.bing.com/campaign{numRowsReturned}");
                            results.Add(rowResults);
                        }
                    }
                    break;
                case DimensionReportType.SearchTerms:
                    //Note: Keyword name requires same logic as Ad group name; will decide later whether we will check for this or not
                    if (callType == CallType.All)
                    {
                        //Only 1 row returned here
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.SearchTerm, "SearchTerm1");
                        rowResults.Add(InlineDownloadTestHeaders.CampaignCampaignType, "Search");
                        results.Add(rowResults);
                    }
                    else
                    {
                        //2 rows returned for Default and Mandatory
                        for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                        {
                            Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                            rowResults.Add(InlineDownloadTestHeaders.SearchTerm, $"SearchTerm{numRowsReturned}");
                            results.Add(rowResults);
                        }
                    }
                    break;
                case DimensionReportType.DestinationUrl:
                    if (callType == CallType.All)
                    {
                        //Only 1 row returned here
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.KeywordDestinationUrl, "");
                        rowResults.Add(InlineDownloadTestHeaders.FinalUrls, "https://www.bing.com/final/url/ordinal_1");
                        rowResults.Add(InlineDownloadTestHeaders.UrlCustomParameters, "{_customparam/1key}=customparam/1value");
                        rowResults.Add(InlineDownloadTestHeaders.TrackingTemplate, "trackingtemplate/1");
                        results.Add(rowResults);
                    }
                    else
                    {
                        //2 rows returned for Default and Mandatory
                        for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                        {
                            Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                            string stringValue = (numRowsReturned % 2) == 0 ? $"https://www.bing.com/destination/url/ordinal_{numRowsReturned}" : $"";
                            rowResults.Add(InlineDownloadTestHeaders.KeywordDestinationUrl, stringValue);
                            if (callType == CallType.Default)
                            {
                                stringValue = (numRowsReturned % 2) == 0 ? $"" : $"https://www.bing.com/final/url/ordinal_{numRowsReturned}";
                                rowResults.Add(InlineDownloadTestHeaders.FinalUrls, stringValue);
                            }
                            results.Add(rowResults);
                        }
                    }
                    break;
                case DimensionReportType.CallForwardingDetail:
                    //With filters, 2 rows are still returned, unlike other calls
                    for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                    {
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.CallStartTime, DateTime.Today.AddDays(-2).AddHours(-(1 + numRowsReturned)));
                        rowResults.Add(InlineDownloadTestHeaders.CallEndTime, DateTime.Today.AddDays(-2).AddMinutes(-30));
                        rowResults.Add(InlineDownloadTestHeaders.CallStatus, "Received");
                        rowResults.Add(InlineDownloadTestHeaders.CallDuration, 1800 + (1800 * numRowsReturned * 2));
                        if (callType == CallType.All)
                        {
                            rowResults.Add(InlineDownloadTestHeaders.AreaCode, 123);
                            rowResults.Add(InlineDownloadTestHeaders.City, "Call City");
                            rowResults.Add(InlineDownloadTestHeaders.State, "Call State");
                        }
                        results.Add(rowResults);
                    }
                    break;
                case DimensionReportType.UserLocations:
                    if (callType == CallType.All)
                    {
                        for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                        {
                            //Radius serves as a dimension w/ different values here, although all other values will match
                            Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                            rowResults.Add(InlineDownloadTestHeaders.Country, "United States");
                            rowResults.Add(InlineDownloadTestHeaders.State, "Washington");
                            rowResults.Add(InlineDownloadTestHeaders.Metro, "Seattle");
                            rowResults.Add(InlineDownloadTestHeaders.City, "Seattle");
                            rowResults.Add(InlineDownloadTestHeaders.County, "King");
                            rowResults.Add(InlineDownloadTestHeaders.PostalCode, "98103");
                            rowResults.Add(InlineDownloadTestHeaders.TargetLocation, "");
                            int radius = (numRowsReturned%2) == 0 ? 0 : 5;
                            rowResults.Add(InlineDownloadTestHeaders.Radius, radius);
                            results.Add(rowResults);
                        }
                    }
                    else
                    {
                        //2 rows returned for Default and Mandatory
                        for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                        {
                            Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                            string expected = (numRowsReturned % 2) == 0 ? "United States" : "Canada";
                            rowResults.Add(InlineDownloadTestHeaders.Country, expected);
                            if (callType == CallType.Default)
                            {
                                expected = (numRowsReturned % 2) == 0 ? "Washington" : "Ontario";
                                rowResults.Add(InlineDownloadTestHeaders.State, expected);
                                expected = (numRowsReturned % 2) == 0 ? "Seattle" : "";
                                rowResults.Add(InlineDownloadTestHeaders.Metro, expected);
                                expected = (numRowsReturned % 2) == 0 ? "Seattle" : "Toronto";
                                rowResults.Add(InlineDownloadTestHeaders.City, expected);
                            }
                            results.Add(rowResults);
                        }
                    }
                    break;
                case DimensionReportType.Geographic:
                    if (callType == CallType.All)
                    {
                        //Only 1 row returned here
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.Country, "Canada");
                        rowResults.Add(InlineDownloadTestHeaders.State, "Ontario");
                        rowResults.Add(InlineDownloadTestHeaders.Metro, "");
                        rowResults.Add(InlineDownloadTestHeaders.City, "Toronto");
                        rowResults.Add(InlineDownloadTestHeaders.County, "");
                        rowResults.Add(InlineDownloadTestHeaders.PostalCode, "");
                        rowResults.Add(InlineDownloadTestHeaders.TargetLocation, "lat 50 lon 60");
                        rowResults.Add(InlineDownloadTestHeaders.LocationType, "Unknown");
                        rowResults.Add(InlineDownloadTestHeaders.MostSpecificLocation, "Toronto");
                        rowResults.Add(InlineDownloadTestHeaders.Radius, 5);
                        results.Add(rowResults);
                    }
                    else
                    {
                        //2 rows returned for Default and Mandatory
                        for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                        {
                            Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                            string expected = (numRowsReturned % 2) == 0 ? "United States" : "Canada";
                            rowResults.Add(InlineDownloadTestHeaders.Country, expected);
                            if (callType == CallType.Default)
                            {
                                expected = (numRowsReturned % 2) == 0 ? "Washington" : "Ontario";
                                rowResults.Add(InlineDownloadTestHeaders.State, expected);
                                expected = (numRowsReturned % 2) == 0 ? "Seattle" : "";
                                rowResults.Add(InlineDownloadTestHeaders.Metro, expected);
                                expected = (numRowsReturned % 2) == 0 ? "Seattle" : "Toronto";
                                rowResults.Add(InlineDownloadTestHeaders.City, expected);
                                rowResults.Add(InlineDownloadTestHeaders.LocationType, "Unknown");
                            }
                            results.Add(rowResults);
                        }
                    }
                    break;
                case DimensionReportType.ShoppingBrand:
                    for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                    {
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.Brand, $"Brand {numRowsReturned}");
                        results.Add(rowResults);
                    }
                    break;
                case DimensionReportType.ShoppingCategory:
                    for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                    {
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.CategoryL1, $"OfferCategoryL1 {numRowsReturned}");
                        results.Add(rowResults);
                    }
                    break;
                case DimensionReportType.ShoppingItemId:
                    for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                    {
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.MerchantIdentifier, $"MerchantProductId {numRowsReturned}");
                        results.Add(rowResults);
                    }
                    break;
                case DimensionReportType.ShoppingProductType:
                    for (int numRowsReturned = 0; numRowsReturned < 2; numRowsReturned++)
                    {
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.ProductType1, $"ProductTypeL1 {numRowsReturned}");
                        results.Add(rowResults);
                    }
                    break;
                case DimensionReportType.ShoppingStoreId:
                    for (int numRowsReturned = 0; numRowsReturned < (callType == CallType.All ? 2:1); numRowsReturned++)
                    {
                        Dictionary<string, Object> rowResults = new Dictionary<string, Object>();
                        rowResults.Add(InlineDownloadTestHeaders.ProviderId, $"[{(long)customerInfo.AccountIds[0] * 10}]");
                        results.Add(rowResults);

                        if (callType == CallType.All)
                        {
                            rowResults.Add(InlineDownloadTestHeaders.Language, "English");
                        }
                    }
                    break;
            }

            return results;
        }

        internal void ValidateResults(BulkCsvParser csvParser, List<Dictionary<string, Object>> expectedResults, DimensionReportType reportType, CallType callType = CallType.Default)
        {
            string uniqueColumn = "";
            switch (reportType)
            {
                case DimensionReportType.AgeAndGender:
                    uniqueColumn = InlineDownloadTestHeaders.AgeGroup;
                    break;
                case DimensionReportType.PublisherWebsite:
                    uniqueColumn = InlineDownloadTestHeaders.PublisherWebsite;
                    break;
                case DimensionReportType.SearchTerms:
                    uniqueColumn = InlineDownloadTestHeaders.SearchTerm;
                    break;
                case DimensionReportType.DestinationUrl:
                    uniqueColumn = InlineDownloadTestHeaders.KeywordDestinationUrl;
                    break;
                case DimensionReportType.CallForwardingDetail:
                    uniqueColumn = InlineDownloadTestHeaders.CallStartTime;
                    break;
                case DimensionReportType.UserLocations:
                    uniqueColumn = InlineDownloadTestHeaders.Country; //Can change depending on test parameters
                    if (callType == CallType.All)
                    {
                        uniqueColumn = InlineDownloadTestHeaders.Radius;
                    }
                    break;
                case DimensionReportType.Geographic:
                    uniqueColumn = InlineDownloadTestHeaders.Country; //Can change depending on test parameters
                    break;
                case DimensionReportType.ShoppingBrand:
                    uniqueColumn = InlineDownloadTestHeaders.Brand;
                    break;
                case DimensionReportType.ShoppingCategory:
                    uniqueColumn = InlineDownloadTestHeaders.CategoryL1;
                    break;
                case DimensionReportType.ShoppingItemId:
                    uniqueColumn = InlineDownloadTestHeaders.MerchantIdentifier;
                    break;
                case DimensionReportType.ShoppingProductType:
                    uniqueColumn = InlineDownloadTestHeaders.ProductType1;
                    break;
                case DimensionReportType.ShoppingStoreId:
                    uniqueColumn = InlineDownloadTestHeaders.ProviderId;
                    break;
                default:
                    Assert.Fail();
                    break;
            }

            Assert.AreEqual(expectedResults.Count(), csvParser.NormalRowsCount);
            for (int index = 0; index < csvParser.NormalRowsCount; index++)
            {
                //var expectedRow = expectedResults.ElementAt(index);
                var actualRow = csvParser.GetRow(index);
                Dictionary<string, Object> expectedRow = null;
                foreach (var row in expectedResults)
                {
                    if (reportType == DimensionReportType.DestinationUrl && callType != CallType.Mandatory && row[uniqueColumn].Equals(""))
                    {
                        //Some rows will have Final Url vs. Destination Url
                        uniqueColumn = InlineDownloadTestHeaders.FinalUrls;
                    }
                    if (Convert.ChangeType(actualRow.ItemArray[Array.IndexOf(headers.ToArray(), uniqueColumn)], row[uniqueColumn].GetType()).Equals(row[uniqueColumn]))
                    {
                        expectedRow = row;
                        break;
                    }
                }
                Assert.IsNotNull(expectedRow);
                foreach (string column in expectedRow.Keys)
                {
                    var expected = expectedRow[column];
                    var actual = actualRow.ItemArray[Array.IndexOf(headers.ToArray(), column)];
                    Assert.IsNotNull(actual);
                    actual = Convert.ChangeType(actual, expected.GetType());
                    Assert.AreEqual(expected, actual);
                }
            }
        }
    }
}