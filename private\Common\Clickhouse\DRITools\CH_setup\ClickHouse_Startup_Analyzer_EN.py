#!/usr/bin/env python3
"""
ClickHouse Startup Log Analyzer
Analyzes ClickHouse startup process, including:
- Part loading time and count (table level)
- Mutation operations duration
- Background job execution
- Memory usage
- Errors and warnings
"""

import re
import sys
import json
import argparse
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Optional
import pandas as pd

class ClickHouseStartupAnalyzer:
    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.startup_time = None
        self.ready_time = None
        
        # Data storage
        self.parts_data = defaultdict(lambda: {'count': 0, 'time': 0, 'details': []})
        self.mutations_data = []
        self.jobs_data = []
        self.memory_data = []
        self.errors = []
        self.warnings = []
        self.performance_metrics = {}
        
        # Regex patterns
        self.patterns = {
            'timestamp': r'(\d{2}:\d{2}:\d{2}\.\d{6})',  # HH:MM:SS.microseconds format
            'timestamp_full': r'(\d{4}\.\d{2}\.\d{2} \d{2}:\d{2}:\d{2}(?:\.\d{3,6})?)',  # Full timestamp
            'part_loading': r'Loading data part (\S+) from (\S+)',
            'part_loaded': r'Loaded data part (\S+) from (\S+) in (\d+\.?\d*) ms',
            'part_attach': r'Attached part (\S+) from (\S+)',
            'part_attach_time': r'Attached part (\S+).*took (\d+\.?\d*) ms',
            'mutation': r'Mutation (\S+) for table (\S+\.\S+) took (\d+\.?\d*) ms',
            'mutation_start': r'Starting mutation (\S+) for table (\S+\.\S+)',
            'job_start': r'Starting background job (\S+)',
            'job_finish': r'Finished background job (\S+) in (\d+\.?\d*) ms',
            'memory': r'Memory usage: (\d+\.?\d*) (MB|GB|KB)',
            'error': r'<Error>.*?(\S+): (.+)',
            'warning': r'<Warning>.*?(\S+): (.+)',
            'startup_complete': r'Ready for connections|Application: Ready for connections|Server listening for',
            'table_attach': r'Attaching table (\S+\.\S+)|Loading metadata for table (\S+\.\S+)',
            'table_detach': r'Detaching table (\S+\.\S+)',
            'merge_tree_part': r'MergeTree part (\S+) of table (\S+\.\S+)',
            'background_task': r'Background task (\S+) for table (\S+\.\S+) took (\d+\.?\d*) ms',
            'merge_start': r'Merging (\d+) parts.*in table (\S+\.\S+)',
            'merge_finish': r'Merged (\d+) parts.*in table (\S+\.\S+) in (\d+\.?\d*) ms',
            'disk_space': r'Available disk space: (\d+\.?\d*) (MB|GB|TB)',
            'query_start': r'Query.*started.*query_id = (\S+)',
            'query_finish': r'Query.*finished.*query_id = (\S+).*in (\d+\.?\d*) ms',
            'loading_metadata': r'Loading metadata for table (\S+\.\S+)',
            'table_startup': r'Table (\S+\.\S+) startup time: (\d+\.?\d*) ms',
            'database_attach': r'Attaching database (\S+)',
            'part_count': r'(\d+) parts? in table (\S+\.\S+)',
            'memory_info': r'Available RAM: ([\d.]+) (GiB|MiB|TiB)',
            'cpu_info': r'Available CPU.*cores: (\d+)',
            'application_startup': r'Application: starting up|Application: Starting ClickHouse'
        }
    
    def parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse timestamp string"""
        try:
            # Try parsing full timestamp
            return datetime.strptime(timestamp_str, '%Y.%m.%d %H:%M:%S.%f')
        except ValueError:
            try:
                # Try parsing full timestamp without microseconds
                return datetime.strptime(timestamp_str, '%Y.%m.%d %H:%M:%S')
            except ValueError:
                try:
                    # Try parsing time-only timestamp with microseconds
                    # Since there's no date info, use current date
                    today = datetime.now().date()
                    time_obj = datetime.strptime(timestamp_str, '%H:%M:%S.%f').time()
                    return datetime.combine(today, time_obj)
                except ValueError:
                    try:
                        # Try parsing time-only timestamp without microseconds
                        today = datetime.now().date()
                        time_obj = datetime.strptime(timestamp_str, '%H:%M:%S').time()
                        return datetime.combine(today, time_obj)
                    except ValueError:
                        return None
    
    def analyze_log(self):
        """Analyze log file"""
        print(f"Starting analysis of log file: {self.log_file_path}")
        
        line_count = 0
        with open(self.log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                line_count += 1
                if line_count % 1000000 == 0:
                    print(f"Processed {line_count:,} lines...")
                
                self._process_line(line.strip())
        
        print(f"Log analysis complete, processed {line_count:,} lines")
        self._calculate_metrics()
    
    def _process_line(self, line: str):
        """Process a single log line"""
        # Extract timestamp - try full format first, then short format
        timestamp_match = re.search(self.patterns['timestamp_full'], line)
        if not timestamp_match:
            timestamp_match = re.search(self.patterns['timestamp'], line)
        
        if not timestamp_match:
            return
        
        timestamp = self.parse_timestamp(timestamp_match.group(1))
        if not timestamp:
            return
        
        # Record startup time
        if self.startup_time is None:
            self.startup_time = timestamp
        
        # Check for startup completion
        if re.search(self.patterns['startup_complete'], line):
            self.ready_time = timestamp
        
        # Analyze part loading
        self._analyze_parts(line, timestamp)
        
        # Analyze mutations
        self._analyze_mutations(line, timestamp)
        
        # Analyze jobs
        self._analyze_jobs(line, timestamp)
        
        # Analyze memory usage
        self._analyze_memory(line, timestamp)
        
        # Analyze errors and warnings
        self._analyze_errors_warnings(line, timestamp)
        
        # Analyze table operations
        self._analyze_table_operations(line, timestamp)
        
        # Analyze ClickHouse specific info
        self._analyze_clickhouse_specific(line, timestamp)
    
    def _analyze_parts(self, line: str, timestamp: datetime):
        """Analyze part-related operations"""
        # Part loading completed
        part_loaded_match = re.search(self.patterns['part_loaded'], line)
        if part_loaded_match:
            part_name = part_loaded_match.group(1)
            table_name = part_loaded_match.group(2)
            load_time = float(part_loaded_match.group(3))
            
            self.parts_data[table_name]['count'] += 1
            self.parts_data[table_name]['time'] += load_time
            self.parts_data[table_name]['details'].append({
                'part_name': part_name,
                'load_time': load_time,
                'timestamp': timestamp
            })
        
        # MergeTree Part operations
        merge_part_match = re.search(self.patterns['merge_tree_part'], line)
        if merge_part_match:
            part_name = merge_part_match.group(1)
            table_name = merge_part_match.group(2)
            
            if 'merge_operations' not in self.parts_data[table_name]:
                self.parts_data[table_name]['merge_operations'] = []
            
            self.parts_data[table_name]['merge_operations'].append({
                'part_name': part_name,
                'timestamp': timestamp,
                'operation': 'merge'
            })
    
    def _analyze_mutations(self, line: str, timestamp: datetime):
        """Analyze mutation operations"""
        mutation_match = re.search(self.patterns['mutation'], line)
        if mutation_match:
            mutation_id = mutation_match.group(1)
            table_name = mutation_match.group(2)
            duration = float(mutation_match.group(3))
            
            self.mutations_data.append({
                'mutation_id': mutation_id,
                'table_name': table_name,
                'duration_ms': duration,
                'timestamp': timestamp
            })
    
    def _analyze_jobs(self, line: str, timestamp: datetime):
        """Analyze background tasks"""
        # Job start
        job_start_match = re.search(self.patterns['job_start'], line)
        if job_start_match:
            job_name = job_start_match.group(1)
            self.jobs_data.append({
                'job_name': job_name,
                'start_time': timestamp,
                'status': 'started'
            })
        
        # Job finish
        job_finish_match = re.search(self.patterns['job_finish'], line)
        if job_finish_match:
            job_name = job_finish_match.group(1)
            duration = float(job_finish_match.group(2))
            
            # Find corresponding start record
            for job in reversed(self.jobs_data):
                if job['job_name'] == job_name and job['status'] == 'started':
                    job['end_time'] = timestamp
                    job['duration_ms'] = duration
                    job['status'] = 'completed'
                    break
        
        # Background task
        bg_task_match = re.search(self.patterns['background_task'], line)
        if bg_task_match:
            task_name = bg_task_match.group(1)
            table_name = bg_task_match.group(2)
            duration = float(bg_task_match.group(3))
            
            self.jobs_data.append({
                'job_name': f"bg_{task_name}",
                'table_name': table_name,
                'start_time': timestamp,
                'end_time': timestamp,
                'duration_ms': duration,
                'status': 'completed'
            })
    
    def _analyze_memory(self, line: str, timestamp: datetime):
        """Analyze memory usage"""
        memory_match = re.search(self.patterns['memory'], line)
        if memory_match:
            value = float(memory_match.group(1))
            unit = memory_match.group(2)
            
            # Convert to MB
            if unit == 'GB':
                value *= 1024
            elif unit == 'KB':
                value /= 1024
            
            self.memory_data.append({
                'timestamp': timestamp,
                'memory_mb': value
            })
    
    def _analyze_errors_warnings(self, line: str, timestamp: datetime):
        """Analyze errors and warnings"""
        error_match = re.search(self.patterns['error'], line)
        if error_match:
            component = error_match.group(1)
            message = error_match.group(2)
            self.errors.append({
                'timestamp': timestamp,
                'component': component,
                'message': message
            })
        
        warning_match = re.search(self.patterns['warning'], line)
        if warning_match:
            component = warning_match.group(1)
            message = warning_match.group(2)
            self.warnings.append({
                'timestamp': timestamp,
                'component': component,
                'message': message
            })
    
    def _analyze_table_operations(self, line: str, timestamp: datetime):
        """Analyze table operations"""
        attach_match = re.search(self.patterns['table_attach'], line)
        if attach_match:
            table_name = attach_match.group(1) or attach_match.group(2)
            if 'table_operations' not in self.performance_metrics:
                self.performance_metrics['table_operations'] = []
            
            self.performance_metrics['table_operations'].append({
                'operation': 'attach',
                'table_name': table_name,
                'timestamp': timestamp
            })
    
    def _analyze_clickhouse_specific(self, line: str, timestamp: datetime):
        """Analyze ClickHouse-specific log information"""
        # Application startup
        if re.search(self.patterns['application_startup'], line):
            if 'application_events' not in self.performance_metrics:
                self.performance_metrics['application_events'] = []
            self.performance_metrics['application_events'].append({
                'event': 'application_startup',
                'timestamp': timestamp,
                'line': line.strip()
            })
        
        # Memory info
        memory_match = re.search(self.patterns['memory_info'], line)
        if memory_match:
            try:
                value = float(memory_match.group(1))
                unit = memory_match.group(2)
                
                # Convert to MB
                if unit == 'GiB':
                    value *= 1024
                elif unit == 'TiB':
                    value *= 1024 * 1024
                elif unit == 'MiB':
                    pass  # Already in MB
                
                self.memory_data.append({
                    'timestamp': timestamp,
                    'memory_mb': value,
                    'type': 'system_info'
                })
            except (ValueError, AttributeError):
                pass  # Ignore parsing errors
        
        # CPU info
        cpu_match = re.search(self.patterns['cpu_info'], line)
        if cpu_match:
            cores = int(cpu_match.group(1))
            if 'system_info' not in self.performance_metrics:
                self.performance_metrics['system_info'] = {}
            self.performance_metrics['system_info']['cpu_cores'] = cores
        
        # Database attach
        db_attach_match = re.search(self.patterns['database_attach'], line)
        if db_attach_match:
            db_name = db_attach_match.group(1)
            if 'database_operations' not in self.performance_metrics:
                self.performance_metrics['database_operations'] = []
            self.performance_metrics['database_operations'].append({
                'operation': 'attach_database',
                'database_name': db_name,
                'timestamp': timestamp
            })
        
        # Table startup time
        table_startup_match = re.search(self.patterns['table_startup'], line)
        if table_startup_match:
            table_name = table_startup_match.group(1)
            startup_time = float(table_startup_match.group(2))
            
            if table_name not in self.parts_data:
                self.parts_data[table_name] = {'count': 0, 'time': 0, 'details': []}
            
            self.parts_data[table_name]['startup_time'] = startup_time
            self.parts_data[table_name]['details'].append({
                'operation': 'table_startup',
                'startup_time': startup_time,
                'timestamp': timestamp
            })
        
        # Part count info
        part_count_match = re.search(self.patterns['part_count'], line)
        if part_count_match:
            count = int(part_count_match.group(1))
            table_name = part_count_match.group(2)
            
            if table_name not in self.parts_data:
                self.parts_data[table_name] = {'count': 0, 'time': 0, 'details': []}
            
            self.parts_data[table_name]['part_count_reported'] = count
        
        # Loading metadata
        loading_metadata_match = re.search(self.patterns['loading_metadata'], line)
        if loading_metadata_match:
            table_name = loading_metadata_match.group(1)
            
            if table_name not in self.parts_data:
                self.parts_data[table_name] = {'count': 0, 'time': 0, 'details': []}
            
            self.parts_data[table_name]['details'].append({
                'operation': 'loading_metadata',
                'timestamp': timestamp
            })
    
    def _calculate_metrics(self):
        """Calculate performance metrics"""
        if self.startup_time and self.ready_time:
            self.performance_metrics['total_startup_time'] = (
                self.ready_time - self.startup_time
            ).total_seconds()
        
        # Calculate part loading statistics per table
        for table_name, data in self.parts_data.items():
            if data['count'] > 0:
                data['avg_load_time'] = data['time'] / data['count']
        
        # Calculate mutation statistics
        if self.mutations_data:
            mutation_times = [m['duration_ms'] for m in self.mutations_data]
            self.performance_metrics['mutation_stats'] = {
                'total_count': len(mutation_times),
                'total_time_ms': sum(mutation_times),
                'avg_time_ms': sum(mutation_times) / len(mutation_times),
                'max_time_ms': max(mutation_times),
                'min_time_ms': min(mutation_times)
            }
        
        # Calculate job statistics
        completed_jobs = [j for j in self.jobs_data if j['status'] == 'completed' and 'duration_ms' in j]
        if completed_jobs:
            job_times = [j['duration_ms'] for j in completed_jobs]
            self.performance_metrics['job_stats'] = {
                'total_count': len(job_times),
                'total_time_ms': sum(job_times),
                'avg_time_ms': sum(job_times) / len(job_times),
                'max_time_ms': max(job_times),
                'min_time_ms': min(job_times)
            }
    
    def generate_report(self) -> str:
        """Generate analysis report"""
        report = []
        report.append("=" * 80)
        report.append("ClickHouse Startup Analysis Report")
        report.append("=" * 80)
        
        # Overall startup time
        if 'total_startup_time' in self.performance_metrics:
            startup_time = self.performance_metrics['total_startup_time']
            report.append(f"\n📊 Total Startup Time: {startup_time:.2f} seconds")
            report.append(f"Startup Time: {self.startup_time}")
            report.append(f"Ready Time: {self.ready_time}")
        
        # Part loading analysis
        report.append(f"\n📦 Part Loading Analysis (Total {len(self.parts_data)} tables):")
        report.append("-" * 60)
        
        # Sort by loading time
        sorted_parts = sorted(
            self.parts_data.items(),
            key=lambda x: x[1]['time'],
            reverse=True
        )
        
        for table_name, data in sorted_parts[:20]:  # Show top 20 most time-consuming tables
            if data['count'] > 0:
                report.append(
                    f"Table: {table_name:<40} "
                    f"Parts: {data['count']:>6} "
                    f"Total Time: {data['time']:>8.1f}ms "
                    f"Avg: {data['avg_load_time']:>6.1f}ms"
                )
        
        # Mutation analysis
        if self.mutations_data:
            report.append(f"\n🔄 Mutation Analysis (Total {len(self.mutations_data)}):")
            report.append("-" * 60)
            
            mutation_stats = self.performance_metrics.get('mutation_stats', {})
            if mutation_stats:
                report.append(f"Total Count: {mutation_stats['total_count']}")
                report.append(f"Total Time: {mutation_stats['total_time_ms']:.1f} ms")
                report.append(f"Average Time: {mutation_stats['avg_time_ms']:.1f} ms")
                report.append(f"Maximum Time: {mutation_stats['max_time_ms']:.1f} ms")
                report.append(f"Minimum Time: {mutation_stats['min_time_ms']:.1f} ms")
            
            # Mutations grouped by table
            mutations_by_table = defaultdict(list)
            for mutation in self.mutations_data:
                mutations_by_table[mutation['table_name']].append(mutation['duration_ms'])
            
            report.append("\nMutations by Table:")
            for table_name, durations in sorted(mutations_by_table.items(), 
                                              key=lambda x: sum(x[1]), reverse=True)[:10]:
                total_time = sum(durations)
                avg_time = total_time / len(durations)
                report.append(
                    f"  {table_name:<40} "
                    f"Count: {len(durations):>3} "
                    f"Total Time: {total_time:>8.1f}ms "
                    f"Avg: {avg_time:>6.1f}ms"
                )
        
        # Background job analysis
        completed_jobs = [j for j in self.jobs_data if j['status'] == 'completed' and 'duration_ms' in j]
        if completed_jobs:
            report.append(f"\n⚙️ Background Task Analysis (Total {len(completed_jobs)}):")
            report.append("-" * 60)
            
            job_stats = self.performance_metrics.get('job_stats', {})
            if job_stats:
                report.append(f"Total Count: {job_stats['total_count']}")
                report.append(f"Total Time: {job_stats['total_time_ms']:.1f} ms")
                report.append(f"Average Time: {job_stats['avg_time_ms']:.1f} ms")
                report.append(f"Maximum Time: {job_stats['max_time_ms']:.1f} ms")
                report.append(f"Minimum Time: {job_stats['min_time_ms']:.1f} ms")
            
            # Group by job type
            jobs_by_type = defaultdict(list)
            for job in completed_jobs:
                jobs_by_type[job['job_name']].append(job['duration_ms'])
            
            report.append("\nJobs by Type:")
            for job_name, durations in sorted(jobs_by_type.items(), 
                                            key=lambda x: sum(x[1]), reverse=True)[:10]:
                total_time = sum(durations)
                avg_time = total_time / len(durations)
                report.append(
                    f"  {job_name:<30} "
                    f"Count: {len(durations):>3} "
                    f"Total Time: {total_time:>8.1f}ms "
                    f"Avg: {avg_time:>6.1f}ms"
                )
        
        # Error and warning statistics
        if self.errors:
            report.append(f"\n❌ Error Statistics (Total {len(self.errors)}):")
            report.append("-" * 60)
            error_counts = Counter(error['component'] for error in self.errors)
            for component, count in error_counts.most_common(10):
                report.append(f"  {component:<30} {count:>5} times")
        
        if self.warnings:
            report.append(f"\n⚠️ Warning Statistics (Total {len(self.warnings)}):")
            report.append("-" * 60)
            warning_counts = Counter(warning['component'] for warning in self.warnings)
            for component, count in warning_counts.most_common(10):
                report.append(f"  {component:<30} {count:>5} times")
        
        # Memory usage
        if self.memory_data:
            memory_values = [m['memory_mb'] for m in self.memory_data]
            report.append(f"\n💾 Memory Usage Statistics:")
            report.append("-" * 60)
            report.append(f"Maximum Memory Usage: {max(memory_values):.1f} MB")
            report.append(f"Minimum Memory Usage: {min(memory_values):.1f} MB")
            report.append(f"Average Memory Usage: {sum(memory_values)/len(memory_values):.1f} MB")
        
        # System information
        if 'system_info' in self.performance_metrics:
            system_info = self.performance_metrics['system_info']
            report.append(f"\n🖥️ System Information:")
            report.append("-" * 60)
            if 'cpu_cores' in system_info:
                report.append(f"CPU Cores: {system_info['cpu_cores']}")
        
        # Database operations
        if 'database_operations' in self.performance_metrics:
            db_ops = self.performance_metrics['database_operations']
            report.append(f"\n🗄️ Database Operations (Total {len(db_ops)}):")
            report.append("-" * 60)
            db_counts = {}
            for op in db_ops:
                db_name = op['database_name']
                if db_name not in db_counts:
                    db_counts[db_name] = 0
                db_counts[db_name] += 1
            
            for db_name, count in sorted(db_counts.items(), key=lambda x: x[1], reverse=True):
                report.append(f"  {db_name}: {count} operations")
        
        # Table startup times
        tables_with_startup = {table: data for table, data in self.parts_data.items() if 'startup_time' in data}
        if tables_with_startup:
            report.append(f"\n⏱️ Table Startup Times (Total {len(tables_with_startup)} tables):")
            report.append("-" * 60)
            
            sorted_tables = sorted(tables_with_startup.items(), key=lambda x: x[1]['startup_time'], reverse=True)
            for table_name, data in sorted_tables[:20]:  # Show top 20 most time-consuming tables
                startup_time = data['startup_time']
                part_count = data.get('part_count_reported', 'N/A')
                report.append(f"  {table_name:<50} Startup Time: {startup_time:>8.1f}ms  Parts: {part_count}")
        
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)
    
    def export_detailed_data(self, output_prefix: str):
        """Export detailed data to files"""
        # Export Parts data
        parts_data = []
        for table_name, data in self.parts_data.items():
            for detail in data.get('details', []):
                parts_data.append({
                    'table_name': table_name,
                    'part_name': detail['part_name'],
                    'load_time_ms': detail['load_time'],
                    'timestamp': detail['timestamp']
                })
        
        if parts_data:
            df_parts = pd.DataFrame(parts_data)
            df_parts.to_csv(f"{output_prefix}_parts.csv", index=False)
            print(f"Parts data exported to: {output_prefix}_parts.csv")
        
        # Export Mutations data
        if self.mutations_data:
            df_mutations = pd.DataFrame(self.mutations_data)
            df_mutations.to_csv(f"{output_prefix}_mutations.csv", index=False)
            print(f"Mutations data exported to: {output_prefix}_mutations.csv")
        
        # Export Jobs data
        if self.jobs_data:
            df_jobs = pd.DataFrame(self.jobs_data)
            df_jobs.to_csv(f"{output_prefix}_jobs.csv", index=False)
            print(f"Jobs data exported to: {output_prefix}_jobs.csv")
        
        # Export summary statistics
        summary_data = {
            'performance_metrics': self.performance_metrics,
            'parts_summary': {
                table: {
                    'count': data['count'],
                    'total_time_ms': data['time'],
                    'avg_time_ms': data.get('avg_load_time', 0)
                }
                for table, data in self.parts_data.items()
            }
        }
        
        with open(f"{output_prefix}_summary.json", 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False, default=str)
        print(f"Summary data exported to: {output_prefix}_summary.json")

def main():
    parser = argparse.ArgumentParser(description='ClickHouse Startup Log Analyzer')
    parser.add_argument('log_file', help='Path to ClickHouse log file')
    parser.add_argument('--output', '-o', default='clickhouse_analysis', 
                       help='Output file prefix (default: clickhouse_analysis)')
    parser.add_argument('--export-data', action='store_true',
                       help='Export detailed data to CSV files')
    
    args = parser.parse_args()
    
    try:
        analyzer = ClickHouseStartupAnalyzer(args.log_file)
        analyzer.analyze_log()
        
        # Generate and display report
        report = analyzer.generate_report()
        print(report)
        
        # Save report to file
        with open(f"{args.output}_report.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\nReport saved to: {args.output}_report.txt")
        
        # Export detailed data
        if args.export_data:
            analyzer.export_detailed_data(args.output)
        
    except FileNotFoundError:
        print(f"Error: Log file {args.log_file} not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error during analysis: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
