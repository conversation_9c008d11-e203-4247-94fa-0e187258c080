
namespace Microsoft.Advertising.Advertiser.Api.V2
{
    using System;
    using System.Net;
    using System.IO;

    using System.Collections.Generic;
    using CampaignMiddleTierTest.Framework;
    using Microsoft.VisualStudio.TestTools.UnitTesting;

    using CampaignMiddleTierTest.Framework.TestObjects;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using System.Linq;
    using Microsoft.Advertising.Advertiser.Api.V2.ReportDownload;
    using CampaignMiddleTierTest.Framework.Configuration;
    using CampaignMiddleTierTest.Framework.Clickhouse;

    [TestClass]
    public sealed class DsaMixedModeCategoryGridTest : CampaignTestBase
    {
        private CustomerInfo CustomerInfo;
        private TestCampaignCollection _campaignCollection;
        private TestAdGroupCollection _adgroupCollection;
        private TestAdGroupCollection _adgroupCollectionUncategorized;
        private readonly string BaseUrl = ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})";
        static int RowsPerPage = 20;
        private Random rd = new Random();
        private Dictionary<long, BiData> _biDatas = new Dictionary<long, BiData>();
        private BiData _biDatasUncategorized = new BiData();
        private Dictionary<long, TestCampaign> _campaignDatas = new Dictionary<long, TestCampaign>();
        private Dictionary<long, TestAdGroup> _adGroupDatas = new Dictionary<long, TestAdGroup>();

        public const string ODataUncategorizedCount = "@ns.uncategorized";
        public const string ODataTotalCount = "@ns.domaincategory.totals";
        private const string CategorySeperator = "/";

        [TestInitialize]
        public void TestInit()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }
            CustomerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.DSAPilot, Features.DSAMixedModeCampaign);
            TestDataSetup();
            TestBiDataSetup();
            TestUncategorizedBiDataSetup();
        }

        [TestCleanup]
        public void TestCleanupTo()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            TestBiDataCleanup();
        }      

        [TestMethod, Priority(0)]
        [Owner(TestOwners.DSA)]
        public void GetDsaCategoryWithBiData()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }
            var response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridPagination = new GridPagination { RequestedPage = 1, RowsPerPage = RowsPerPage },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.DSA)]
        public void GetDsaCategoryWithBiData_SortByNonBiColumns()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AdGroupId },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].AdGroupId <= response.GridData.PageData[i].AdGroupId);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AdGroupName },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(string.Compare(response.GridData.PageData[i - 1].AdGroupName, response.GridData.PageData[i].AdGroupName) <= 0);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.CampaignName },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(string.Compare(response.GridData.PageData[i - 1].CampaignName, response.GridData.PageData[i].CampaignName) <= 0);
            }
            
            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.CampaignId },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].CampaignId <= response.GridData.PageData[i].CampaignId);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.TopCategory },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(string.Compare(response.GridData.PageData[i - 1].TopCategory, response.GridData.PageData[i].TopCategory) <= 0);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.FirstCategory },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(string.Compare(response.GridData.PageData[i - 1].FirstCategory, response.GridData.PageData[i].FirstCategory) <= 0);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.SecondCategory },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(string.Compare(response.GridData.PageData[i - 1].SecondCategory, response.GridData.PageData[i].SecondCategory) <= 0);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.CategoryText },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(string.Compare(response.GridData.PageData[i - 1].CategoryText, response.GridData.PageData[i].CategoryText) <= 0);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.DSA)]
        public void GetDsaCategoryWithBiData_SortByBiColumns()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.RevenueOnAdSpend },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.RevenueOnAdSpend <= response.GridData.PageData[i].BiData.RevenueOnAdSpend);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.ConversionRate },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.ConversionRate == null ||
                    response.GridData.PageData[i].BiData.ConversionRate == null ||
                    response.GridData.PageData[i - 1].BiData.ConversionRate <= response.GridData.PageData[i].BiData.ConversionRate);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AdvertiserReportedRevenue },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AdvertiserReportedRevenue <= response.GridData.PageData[i].BiData.AdvertiserReportedRevenue);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.CTR },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.ClickThruRate <= response.GridData.PageData[i].BiData.ClickThruRate);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.Clicks },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.Clicks <= response.GridData.PageData[i].BiData.Clicks);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.Impressions },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.Impressions <= response.GridData.PageData[i].BiData.Impressions);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.TotalEffectiveCost },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.Spent <= response.GridData.PageData[i].BiData.Spent);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.Conversions },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.Conversions <= response.GridData.PageData[i].BiData.Conversions);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AverageCPC },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AverageCPC <= response.GridData.PageData[i].BiData.AverageCPC);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AverageCPM },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AverageCPM <= response.GridData.PageData[i].BiData.AverageCPM);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AveragePosition },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AveragePosition <= response.GridData.PageData[i].BiData.AveragePosition);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.CPA },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.CPA == null ||
                    response.GridData.PageData[i].BiData.CPA == null ||
                    response.GridData.PageData[i - 1].BiData.CPA <= response.GridData.PageData[i].BiData.CPA);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.DSA)]
        public void GetDsaCategoryWithBiData_SortByBiConversionColumns()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AllConversions },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AllConversions == null ||
                    response.GridData.PageData[i].BiData.AllConversions == null ||
                    response.GridData.PageData[i - 1].BiData.AllConversions <= response.GridData.PageData[i].BiData.AllConversions);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.ViewThroughConversions },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.ViewThroughConversions == null ||
                    response.GridData.PageData[i].BiData.ViewThroughConversions == null ||
                    response.GridData.PageData[i - 1].BiData.ViewThroughConversions <= response.GridData.PageData[i].BiData.ViewThroughConversions);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.ViewThroughConversionsRevenue },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.ViewThroughConversionsRevenue == null ||
                    response.GridData.PageData[i].BiData.ViewThroughConversionsRevenue == null ||
                    response.GridData.PageData[i - 1].BiData.ViewThroughConversionsRevenue <= response.GridData.PageData[i].BiData.ViewThroughConversionsRevenue);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.ViewThroughConversionsCPA },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.ViewThroughConversionsCPA == null ||
                    response.GridData.PageData[i].BiData.ViewThroughConversionsCPA == null ||
                    response.GridData.PageData[i - 1].BiData.ViewThroughConversionsCPA <= response.GridData.PageData[i].BiData.ViewThroughConversionsCPA);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.ViewThroughConversionsReturnOnAdSpend },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.ViewThroughConversionsReturnOnAdSpend == null ||
                    response.GridData.PageData[i].BiData.ViewThroughConversionsReturnOnAdSpend == null ||
                    response.GridData.PageData[i - 1].BiData.ViewThroughConversionsReturnOnAdSpend <= response.GridData.PageData[i].BiData.ViewThroughConversionsReturnOnAdSpend);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.ViewThroughConversionsRate },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.ViewThroughConversionsRate == null ||
                    response.GridData.PageData[i].BiData.ViewThroughConversionsRate == null ||
                    response.GridData.PageData[i - 1].BiData.ViewThroughConversionsRate <= response.GridData.PageData[i].BiData.ViewThroughConversionsRate);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AllConversionAdvertiserReportedRevenue },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AllConversionAdvertiserReportedRevenue == null ||
                    response.GridData.PageData[i].BiData.AllConversionAdvertiserReportedRevenue == null ||
                    response.GridData.PageData[i - 1].BiData.AllConversionAdvertiserReportedRevenue <= response.GridData.PageData[i].BiData.AllConversionAdvertiserReportedRevenue);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AllConversionCPA },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AllConversionCPA == null ||
                    response.GridData.PageData[i].BiData.AllConversionCPA == null ||
                    response.GridData.PageData[i - 1].BiData.AllConversionCPA <= response.GridData.PageData[i].BiData.AllConversionCPA);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AllConversionRate },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AllConversionRate == null ||
                    response.GridData.PageData[i].BiData.AllConversionRate == null ||
                    response.GridData.PageData[i - 1].BiData.AllConversionRate <= response.GridData.PageData[i].BiData.AllConversionRate);
            }

            response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridSort = new GridSort { SortByGridColumn = GridColumn.AllConversionRevenueOnAdSpend },
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
            for (int i = 1; i < response.GridData.TotalRows; i++)
            {
                Assert.IsTrue(response.GridData.PageData[i - 1].BiData.AllConversionRevenueOnAdSpend == null ||
                    response.GridData.PageData[i].BiData.AllConversionRevenueOnAdSpend == null ||
                    response.GridData.PageData[i - 1].BiData.AllConversionRevenueOnAdSpend <= response.GridData.PageData[i].BiData.AllConversionRevenueOnAdSpend);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.DSA)]
        public void GetDsaCategoryWithBiData_FilterByImpressionsOrClicks()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var response = TestReportingAggregator.GetDsaCategoryWithBiData(CustomerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridFilterBooleanOperator = BooleanFilterOperator.Or,
                    GridFilter = new[]
                    {
                        new GridFilterExpression
                        {
                            ColumnToFilter = GridColumn.Impressions,
                            FilterOperation = FilterOperation.Greater,
                            FilterOnValues = new object[] { 0 }
                        },
                        new GridFilterExpression
                        {
                            ColumnToFilter = GridColumn.Clicks,
                            FilterOperation = FilterOperation.Greater,
                            FilterOnValues = new object[] { 0 }
                        },
                    }
                };
            });

            ResponseValidator.ValidateBasicSuccess(response);
            Assert.IsNotNull(response.GridData);
            Assert.AreEqual(_biDatas.Count, response.GridData.TotalRows);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        [Ignore] //deprecated 
        public void ReportDownload_DsaMixedModeCategory_E2E()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }
            ReportRule_DsaMixedModeCategory_Execution(CustomerInfo);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        [Ignore] //deprecated 
        public void ReportDownload_DsaMixedModeCategory_E2E_CategoryLevel()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }
            ReportRule_DsaMixedModeCategory_Execution(CustomerInfo, true, true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_Success()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var response = GetCategoryReport(CustomerInfo);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_Pagination_Success()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            //**************** 1. get ALl result
            string orderby = $"Level1Name desc";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, orderby);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);
            Assert.AreEqual((int)countResult, _biDatas.Count);


            //**************** 2. Pagination: get Top 3
            response = GetCategoryReport(CustomerInfo, null, null, null, null, orderby, null, 3, 0);
            Assert.IsNotNull(response);

            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, 3);
            Assert.AreEqual((int)countResult, _biDatas.Count);
            VerifyResponseData(response);

            //**************** 3. Pagination: get Top 3, skip 3
            response = GetCategoryReport(CustomerInfo, null, null, null, null, orderby, null, 3, 3);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, 3);
            Assert.AreEqual((int)countResult, _biDatas.Count);
            VerifyResponseData(response);


            //**************** 4. Pagination: get Top 3, skip 6
            response = GetCategoryReport(CustomerInfo, null, null, null, null, orderby, null, 3, 6);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, 3);
            Assert.AreEqual((int)countResult, _biDatas.Count);
            VerifyResponseData(response);


            //**************** 5. Pagination: get Top 3, skip 6, for cache
            response = GetCategoryReport(CustomerInfo, null, null, null, null, orderby, null, 3, 6);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, 3);
            Assert.AreEqual((int)countResult, _biDatas.Count);
            VerifyResponseData(response);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_WithCache_Success()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var response = GetCategoryReport(CustomerInfo);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response);

            response = GetCategoryReport(CustomerInfo);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_Deleted_Campaign_Success()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var res =  _campaignCollection.Delete(CustomerInfo);
            var response = GetCategoryReport(CustomerInfo);
            Assert.IsNotNull(response);

            var deleteTotals = new BiData();
            foreach (var key in _biDatas.Keys)
            {
                deleteTotals += _biDatas[key];
            }

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 0);

            //validate parital conversions in annotation
            VerifyAnnotationPartialConversionMetrics(response);

            //validate deleted data
            var deletedNode = response[ODataTotalCount];
            Assert.AreEqual(deleteTotals.AdvertiserReportedRevenue, (double?)deletedNode.DeletedItemsTotals.AdvertiserReportedRevenue, "Incorrect AdvertiserReportedRevenue");
            Assert.AreEqual(deleteTotals.Conversions, (long?)deletedNode.DeletedItemsTotals.Conversions, "Incorrect conversions");
            Assert.AreEqual(deleteTotals.Clicks, (long?)deletedNode.DeletedItemsTotals.Clicks, "Incorrect impressions");

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByCampaignId_Success()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var campaignId = _campaignCollection.Campaigns[0].Data.Id;

            var response = GetCategoryReport(CustomerInfo, campaignId);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAdgroupId_Success()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            var campaignId = _campaignCollection.Campaigns[0].Data.Id;
            var adgroupId = _adgroupCollection.AdGroups[0].Data.Id;
            var response = GetCategoryReport(CustomerInfo, campaignId, adgroupId);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response, true, false, true, false);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCampaignName()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            string filter = $"contains(Campaign/Name ,'{"001"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCampaignName_NULL()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            string filter = $"contains(Campaign/Name ,'{"002"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 0);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCategoryText()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(CategoryText ,'{"Microsoft1"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByPerformanceMetrics_Clicks()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = "PerformanceMetrics/Clicks gt 1223";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 8);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByPerformanceMetrics_Conversions()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = "PerformanceMetrics/Conversions gt 123";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 9);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByPerformanceMetrics_AllConversions()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = "PerformanceMetrics/AllConversions gt 123";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            for (int i = 0; i < response.value.Count; i++)
            {
                Assert.IsTrue(response.value[i].PerformanceMetrics.AllConversions > 123);
            }

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByLevel1Name()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(Level1Name ,'{"Microsoft2"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByLevel1Name_TopLevelCategory()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(Level1Name ,'{"Microsoft2"}') and contains(CategoryLevel ,'{"TopLevelCategory"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response, true, false, true, true, 0);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByLevel1Name_TopLevelCategorySwitch()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(Level1Name ,'{"Microsoft2"}') and contains(CategoryLevel ,'{"TopLevelCategory"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response, true, false, true, true, 0);

            ////////////////////////////////////////////////////////
            filter = $"contains(Level1Name ,'{"Microsoft2"}') and contains(CategoryLevel ,'{"FirstLevelCategory"}')";
            response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response, true, false, true, true, 1);

            ////////////////////////////////////////////////////////
            filter = $"contains(Level1Name ,'{"Microsoft2"}') and contains(CategoryLevel ,'{"FirstLevelCategory"}')";
            response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response, true, false, true, true, 1);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCategoryLevel_TopLevelCategory()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(CategoryLevel ,'{"TopLevelCategory"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 0);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCategoryLevel_FirstLevelCategory()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(CategoryLevel ,'{"FirstLevelCategory"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 1);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCategoryLevel_CategorySwitch()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }


            //////////////////////////////////////////////////////////////////////////////////////////
            //TopLevelCategory
            string filter = $"contains(CategoryLevel ,'{"TopLevelCategory"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 0);

            //////////////////////////////////////////////////////////////////////////////////////////
            //FirstLevelCategory
            filter = $"contains(CategoryLevel ,'{"FirstLevelCategory"}')";
            response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 1);

            //////////////////////////////////////////////////////////////////////////////////////////

            //SecondLevelCategory
            filter = $"contains(CategoryLevel ,'{"SecondLevelCategory"}')";
            response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 2);

            //////////////////////////////////////////////////////////////////////////////////////////

            //SecondLevelCategory: cache
            filter = $"contains(CategoryLevel ,'{"SecondLevelCategory"}')";
            response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 2);

            //////////////////////////////////////////////////////////////////////////////////////////

            //TopLevelCategory
            filter = $"contains(CategoryLevel ,'{"TopLevelCategory"}')";
            response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 0);

            //////////////////////////////////////////////////////////////////////////////////////////
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCategoryLevel_SecondLevelCategory()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(CategoryLevel ,'{"SecondLevelCategory"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, false, false, true, 2);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByLevel1Name_NULL()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(Level1Name ,'{"Google2"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 0);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCampaignName_OrderBy_Level1Name_Desc()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            string filter = $"contains(Campaign/Name ,'{"001"}')";
            string orderby = $"Level1Name desc";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, orderby, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, true, false);
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCampaignName_OrderBy_Clicks_Desc()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            string filter = $"contains(Campaign/Name ,'{"001"}')";
            string orderby = $"PerformanceMetrics/Clicks desc";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, orderby, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, true, false);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByCampaignName_OrderBy_Level1Name_asc()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            string filter = $"contains(Campaign/Name ,'{"001"}')";
            string orderby = $"Level1Name asc";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, _biDatas.Count);

            //validate response
            VerifyResponseData(response, true, true, true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByLevel2Name()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(Level2Name ,'{"Bing1"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByLevel3Name()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(Level3Name ,'{"Ads1"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 1);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByLevel3Name_NULL()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(Level3Name ,'{"Adss"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(count, 0);

            //validate response
            VerifyResponseData(response);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.DSA)]
        public void GetDsaMixedModeCategory_OData_ByAccountId_FilterByAdgroupName()
        {
            if (!TestEnvConfiguration.ENVName_CI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }

            string filter = $"contains(AdGroup/Name ,'{"002"}')";
            var response = GetCategoryReport(CustomerInfo, null, null, null, null, null, filter);
            Assert.IsNotNull(response);

            //validate the count
            dynamic countResult = response[ApiHelper.ODataCount];
            Assert.IsNotNull(countResult, "@odata.count is missing");
            var count = (int)response.value.Count;
            Assert.AreEqual(1, count);

            //validate response
            VerifyResponseData(response);
        }

        public dynamic GetCategoryReport(
          CustomerInfo cInfo,
          long? campaignId = null,
          long? adGroupId = null,
          DateTime? startDate = null,
          DateTime? endDate = null,
          string orderBy = null,
          string filter = null,
          int top = 20,
          int skip = 0,
          bool forceRefreshCache = false,
          HttpStatusCode expectedHttpStatusCode = HttpStatusCode.OK,
          string cacheId = null)
        {
            var startdateStr = startDate?.ToString("MM/dd/yyyy") ?? DateTime.Today.AddDays(-2).ToString("MM/dd/yyyy");
            var enddateStr = endDate?.ToString("MM/dd/yyyy") ?? DateTime.Today.AddDays(2).ToString("MM/dd/yyyy");

            string url;
            if (campaignId.HasValue)
            {
                if (adGroupId.HasValue)
                {
                    url = string.Format(BaseUrl + "/Campaigns({2})/AdGroups({3})", cInfo.CustomerId, cInfo.AccountIds[0],
                        campaignId.Value, adGroupId.Value);
                }
                else
                {
                    url = string.Format(BaseUrl + "/Campaigns({2})", cInfo.CustomerId, cInfo.AccountIds[0],
                        campaignId.Value);
                }
            }
            else
            {
                url = string.Format(BaseUrl, cInfo.CustomerId, cInfo.AccountIds[0]);
            }

            url = string.Format(url + "/DomainCategorys?$&startdate={0}&enddate={1}&$top={2}&$skip={3}", startdateStr,
                enddateStr, top, skip);

            if (orderBy != null)
            {
                url = string.Format(url + "&$orderby={0}", orderBy);
            }

            if (filter != null)
            {
                url = string.Format(url + "&$filter={0}", filter);
            }

            //url = string.Format(url + "&$select=Status,Id,StatusTest");

            url = string.Format(url + "&$expand=Campaign,AdGroup");

            return ApiHelper.CallApi(cInfo, c => c.GetAsync(url),
                e => Assert.AreEqual(expectedHttpStatusCode, e.StatusCode), cacheId, cacheOptions: new AggregatorCacheOptions(forceRefreshCache, null));
        }

        private void ReportRule_DsaMixedModeCategory_Execution(CustomerInfo customerInfo, bool setupData = true, bool categoryLevelFilter = false)
        {
            DateTime startDate = DateTime.Today.AddDays(-30);
            DateTime endDate = DateTime.Today.AddDays(1);

            string entity = "DomainCategory";
            const string format = "Csv";

            string[] headers =
            {
                InlineDownloadTestHeaders.CampaignName,
                InlineDownloadTestHeaders.AdGroupName,
                InlineDownloadTestHeaders.Level1Name,
                InlineDownloadTestHeaders.Level2Name,
                InlineDownloadTestHeaders.Level3Name,
                InlineDownloadTestHeaders.CategoryText,
                InlineDownloadTestHeaders.Clicks,
                InlineDownloadTestHeaders.Impressions,
                InlineDownloadTestHeaders.CTR,
                InlineDownloadTestHeaders.AverageCPC,
                InlineDownloadTestHeaders.AverageCPM,
                InlineDownloadTestHeaders.TotalEffectiveCost,
                InlineDownloadTestHeaders.AveragePosition,
                InlineDownloadTestHeaders.Conversions,
                InlineDownloadTestHeaders.CPA,
                InlineDownloadTestHeaders.ConversionRate,
                InlineDownloadTestHeaders.AdvertiserReportedRevenue,
                InlineDownloadTestHeaders.RevenueOnAdSpend,
            };

            var allConversionsHeaders = new[]
            {
                InlineDownloadTestHeaders.AllConversions,
                InlineDownloadTestHeaders.AllConversionAdvertiserReportedRevenue,
            };

            headers = headers.Concat(allConversionsHeaders).ToArray();

            string queryString =
                "$select=Campaign/Name,AdGroup/Name,Level1Name,Level2Name,Level3Name,CategoryText,PerformanceMetrics/Clicks, PerformanceMetrics/Impressions, PerformanceMetrics/CTR, PerformanceMetrics/AverageCPC, PerformanceMetrics/AverageCPM, PerformanceMetrics/Spend, PerformanceMetrics/AveragePosition, PerformanceMetrics/Conversions, PerformanceMetrics/CPA, PerformanceMetrics/ConversionRate, PerformanceMetrics/AdvertiserReportedRevenue, PerformanceMetrics/RevenueOnAdSpend" +
                ",PerformanceMetrics/AllConversions,PerformanceMetrics/AllConversionAdvertiserReportedRevenue" +
                "&$expand=Campaign,AdGroup";

            if (categoryLevelFilter)
            {
                string filter = $"&$filter=contains(CategoryLevel ,'{"TopLevelCategory"}')";
                queryString += filter;
            }

            var filePath = ReportDownloadTestHelper.ExecuteReportDownload(customerInfo, entity, format, headers,
                queryString, 10, 6, TestContext.TestRunDirectory, startDate, endDate);
            Assert.IsTrue(File.Exists(filePath), $"Downloaded filepath {filePath} does not have file.");

            var csvParser = new BulkCsvParser(filePath, BulkCsvParser.Delimiters.Comma,
                expectedHeader: string.Join(BulkCsvParser.Delimiters.Comma.ToString(), headers));

            var response = TestReportingAggregator.GetDsaCategoryWithBiData(customerInfo, action: request =>
            {
                request.GridDataSelection = new GridDataSelection()
                {
                    GridPagination = new GridPagination { RequestedPage = 1, RowsPerPage = 20 },
                };
            });
            ResponseValidator.ValidateBasicSuccess(response);

            //expecting rows for each DsaTarget + rows for subtotals (search, content, deleted, overall, and possible Native)
            Assert.IsTrue(response.GridData.TotalRows + 4 == csvParser.NormalRowsCount ||
                          response.GridData.TotalRows + 5 == csvParser.NormalRowsCount);
        }

        private void TestDataSetup()
        {
            _campaignCollection = new TestCampaignCollection(3,
                CampaignFactory.CampaignType.Default, CampaignFactory.CampaignSettings.DynamicSearchAdsValid,
                CampaignSettingsFactory.DynamicSearchAdsSetting.DynamicSearchAdsSettingValid,
                CampaignSettingsFactory.DynamicSearchAdsCampaignSettingCount.One,
                CampaignSettingsFactory.ShoppingSettingsCount.None);
            _campaignCollection.Campaigns[0].Data.Name = "Campaign Dsa Category 001";
            _campaignCollection.Campaigns[1].Data.Name = "Campaign Dsa Category 002";
            _campaignCollection.Campaigns[2].Data.Name = "Campaign Dsa Category 003";

            var campaignresponse = _campaignCollection.Add(CustomerInfo);
            Assert.IsTrue(EntityValidator.ValidateBatchSuccess(campaignresponse));

            _adgroupCollection = new TestAdGroupCollection(_campaignCollection.Campaigns[0], 0);
            _adgroupCollection.Add(CustomerInfo, request =>
            {
                for (int index = 0; index < 10; ++index)
                {
                    var adGroup = TestSetting.Factories.TestAdGroupFactory.Produce(AdGroupFactory.BidTarget.Keyword,
                    AdGroupFactory.PublisherCountries.Unspecified, AdGroupFactory.AdGroupType.SearchDynamic);
                    adGroup.Data.Name = "AdGroup Dsa Category 00" + index;
                    _adgroupCollection.AdGroups.Add(adGroup);
                   
                }
            });

            _adgroupCollectionUncategorized = new TestAdGroupCollection(_campaignCollection.Campaigns[0], 0);
            _adgroupCollectionUncategorized.Add(CustomerInfo, request =>
            {
                for (int index = 0; index < 3; ++index)
                {
                    var adGroup = TestSetting.Factories.TestAdGroupFactory.Produce(AdGroupFactory.BidTarget.Keyword,
                    AdGroupFactory.PublisherCountries.Unspecified, AdGroupFactory.AdGroupType.SearchDynamic);
                    adGroup.Data.Name = "AdGroup Dsa Uncategorized Category 00" + index;
                    _adgroupCollectionUncategorized.AdGroups.Add(adGroup);

                }
            });
        }

        private void TestBiDataSetup()
        {
            List<BiData> expectedDataList = new List<BiData>();
            _biDatas = new Dictionary<long, BiData>();

            for (int i = 0; i < _adgroupCollection.AdGroups.Count; i++)
            {
                var biData = new BiData
                {
                    CampaignId = (int)_campaignCollection.Campaigns[0].Data.Id,
                    OrderId = _adgroupCollection.AdGroups[i].Data.Id,
                };

                biData.SetColumnValue(GridColumn.Conversions, i * 1000 + 112);
                biData.SetColumnValue(GridColumn.Clicks, i * 1000 + 223);
                biData.SetColumnValue(GridColumn.AdvertiserReportedRevenue, i * 1000 + 334);
                biData.SetColumnValue(GridColumn.AllConversions, i * 1000 + 444);
                biData.SetColumnValue(GridColumn.AllConversionAdvertiserReportedRevenue, i * 1000 + 534);

                int randomVal = rd.Next(1, 99999);
                biData.AdLandingPageUrlId = randomVal;

                string TopLevelCategory = "Microsoft" + i + randomVal;
                biData.TopLevelCategory = TopLevelCategory;
                biData.FirstLevelCategory = "Bing" + i + randomVal;
                biData.SecondLevelCategory = "Ads" + i + randomVal;

                biData.CalculateDependeningFields();
                expectedDataList.Add(biData);
                _biDatas[_adgroupCollection.AdGroups[i].Data.Id] = biData;
                _campaignDatas[_adgroupCollection.AdGroups[i].Data.Id] = _campaignCollection.Campaigns[0];
                _adGroupDatas[_adgroupCollection.AdGroups[i].Data.Id] = _adgroupCollection.AdGroups[i];
            }
            BiDatabaseHelper.SetDsaCategoryMockBiData(CustomerInfo, expectedDataList);
        }

        private void TestBiDataCleanup()
        {
            BiDatabaseHelper.ClearDsaCategorySummaryDataNew(CustomerInfo);
        }

        private void TestUncategorizedBiDataSetup()
        {
            List<BiData> expectedDataList = new List<BiData>();

            for (int i = 0; i < _adgroupCollectionUncategorized.AdGroups.Count; i++)
            {
                var biData = new BiData
                {
                    CampaignId = (int)_campaignCollection.Campaigns[0].Data.Id,
                    OrderId = _adgroupCollectionUncategorized.AdGroups[i].Data.Id,
                };

                biData.SetColumnValue(GridColumn.Conversions, i * 2000 + 112);
                biData.SetColumnValue(GridColumn.Clicks, i * 2000 + 223);
                biData.SetColumnValue(GridColumn.AdvertiserReportedRevenue, i * 2000 + 334);

                int randomVal = rd.Next(1, 99999);
                biData.AdLandingPageUrlId = randomVal;

                biData.CalculateDependeningFields();
                expectedDataList.Add(biData);

                _biDatasUncategorized += biData;
            }
            BiDatabaseHelper.SetDsaCategoryMockBiData(CustomerInfo, expectedDataList, false);
        }

        private void VerifyResponseData(dynamic result, bool validateBiData = true, bool isSort = false, bool isAsc = true, bool isValidateUncategorizedCategoryeMetrics = true, int caterogyLevel = 2)
        {
            for (int i = 0; i < result.value.Count; i++)
            {
                this.VerifyEntity(result.value[i]);
                if (mockReporting && validateBiData)
                {
                    VerifyCategoryeMetrics(result.value[i], caterogyLevel);
                    VerifyMockPerformanceMetrics(result.value[i], _adgroupCollection.AdGroups.ToList<TestAdGroup>().FindIndex(adgroup => (long)adgroup.Data.Id == (long)result.value[i].AdGroup.Id), isSort, isAsc);
                }
            }

            if (isValidateUncategorizedCategoryeMetrics)
            {
                VerifyUncategorizedCategoryeMetrics(result[ODataUncategorizedCount]);
            }
        }

        private void VerifyEntity(dynamic actual)
        {
            var adGroupId = (long)actual.AdGroup.Id;
            var biData = _biDatas[adGroupId];
            var campaignData = _campaignDatas[adGroupId];
            var adgroupData = _adGroupDatas[adGroupId];

            Assert.IsNotNull(biData);
            Assert.IsNotNull(campaignData);
            Assert.IsNotNull(adgroupData);

            Assert.AreEqual(campaignData.Data.Name, (string)actual.Campaign.Name, "Incorrect Campaign.Name");
            Assert.AreEqual(adgroupData.Data.Name, (string)actual.AdGroup.Name, "Incorrect AdGroup.Name");
            Assert.AreEqual(campaignData.Data.Id, (Int64)actual.Campaign.Id, "Incorrect Campaign.Id");
            Assert.AreEqual(adgroupData.Data.Id, (Int64)actual.AdGroup.Id, "Incorrect AdGroup.Id");
        }

        private void VerifyMockPerformanceMetrics(dynamic actual, int index, bool isSort = false, bool isAsc = true)
        {
            Assert.IsNotNull(actual, "Incorrect nulll PerformanceMetrics");

            var adGroupId = (long)actual.AdGroup.Id;

            if (isSort)
            {
                    adGroupId = _adgroupCollection.AdGroups[index].Data.Id;
            }

            var biData = _biDatas[adGroupId];
            Assert.IsNotNull(biData);

            Assert.AreEqual(biData.AdvertiserReportedRevenue, (double?)actual.PerformanceMetrics.AdvertiserReportedRevenue, "Incorrect AdvertiserReportedRevenue");
            Assert.AreEqual(biData.Conversions, (long?)actual.PerformanceMetrics.Conversions, "Incorrect conversions");
            Assert.AreEqual(biData.Clicks, (long?)actual.PerformanceMetrics.Clicks, "Incorrect impressions");
            Assert.AreEqual(biData.AllConversions, (long?) actual.PerformanceMetrics.AllConversions,
                "Incorrect AllConversions");
            Assert.AreEqual(biData.AllConversionAdvertiserReportedRevenue,
                (double?) actual.PerformanceMetrics.AllConversionAdvertiserReportedRevenue,
                "Incorrect AllConversionAdvertiserReportedRevenue");
        }

        private void VerifyCategoryeMetrics(dynamic actual, int caterogyLevel = 2)
        {
            Assert.IsNotNull(actual, "Incorrect nulll PerformanceMetrics");

            var adGroupId = (long)actual.AdGroup.Id;
            var biData = _biDatas[adGroupId];
            Assert.IsNotNull(biData);

            var categoryText = string.Empty;
            if (caterogyLevel == 2)
            {
                Assert.AreEqual(biData.TopLevelCategory, (string)actual.Level1Name, "Incorrect Level1Name");
                Assert.AreEqual(biData.FirstLevelCategory, (string)actual.Level2Name, "Incorrect Level2Name");
                Assert.AreEqual(biData.SecondLevelCategory, (string)actual.Level3Name, "Incorrect Level3Name");

                categoryText = string.Join(CategorySeperator, biData.TopLevelCategory, biData.FirstLevelCategory, biData.SecondLevelCategory);
            }
            else if (caterogyLevel == 1)
            {
                Assert.AreEqual(biData.TopLevelCategory, (string)actual.Level1Name, "Incorrect Level1Name");
                Assert.AreEqual(biData.FirstLevelCategory, (string)actual.Level2Name, "Incorrect Level2Name");
                Assert.IsNull((string)actual.Level3Name, "SecondLevelCategory should be null if filter TopLevelCategory");

                categoryText = biData.TopLevelCategory + CategorySeperator + biData.FirstLevelCategory;
            }
            else if (caterogyLevel == 0)
            {
                Assert.AreEqual(biData.TopLevelCategory, (string)actual.Level1Name, "Incorrect Level1Name");
                Assert.IsNull((string)actual.Level2Name, "FirstLevelCategory should be null if filter TopLevelCategory");
                Assert.IsNull((string)actual.Level3Name, "SecondLevelCategory should be null if filter TopLevelCategory");

                categoryText = biData.TopLevelCategory;
            }
            Assert.AreEqual(categoryText, (string)actual.CategoryText, "Incorrect CategoryText");
        }

        private void VerifyUncategorizedCategoryeMetrics(dynamic actual)
        {
            Assert.IsNotNull(actual, "Incorrect nulll Uncategorized CategoryeMetrics");
            Assert.IsNotNull(_biDatasUncategorized);
            Assert.IsNotNull(_biDatasUncategorized.AdvertiserReportedRevenue);
            Assert.IsNotNull(_biDatasUncategorized.Conversions);
            Assert.IsNotNull(_biDatasUncategorized.Conversions);

            Assert.AreEqual(_biDatasUncategorized.AdvertiserReportedRevenue, (double?)actual.AdvertiserReportedRevenue, "Incorrect AdvertiserReportedRevenue");
            Assert.AreEqual(_biDatasUncategorized.Conversions, (long?)actual.Conversions, "Incorrect conversions");
            Assert.AreEqual(_biDatasUncategorized.Clicks, (long?)actual.Clicks, "Incorrect impressions");
        }

        private void VerifyAnnotationPartialConversionMetrics(dynamic response)
        {
            Assert.IsNotNull(response["@ns.domaincategory.totals"].TotalsForSearchNonDeletedItems.ConversionsCredit, "Missing ConversionsCredit");
            Assert.IsNotNull(response["@ns.domaincategory.totals"].TotalsForSearchNonDeletedItems.PartialConversionCPA, "Missing PartialConversionCPA");
            Assert.IsNotNull(response["@ns.domaincategory.totals"].TotalsForSearchNonDeletedItems.PartialConversionRate, "Missing PartialConversionRate");
            Assert.IsNotNull(response["@ns.domaincategory.totals"].TotalsForSearchNonDeletedItems.AllConversionsCredit, "Missing AllConversionsCredit");
            Assert.IsNotNull(response["@ns.domaincategory.totals"].TotalsForSearchNonDeletedItems.AllPartialConversionCPA, "Missing AllPartialConversionCPA");
            Assert.IsNotNull(response["@ns.domaincategory.totals"].TotalsForSearchNonDeletedItems.AllPartialConversionRate, "Missing AllPartialConversionRate");

            Assert.IsNotNull(response["@ns.uncategorized"].ConversionsCredit, "Missing ConversionsCredit");
            Assert.IsNotNull(response["@ns.uncategorized"].PartialConversionCPA, "Missing PartialConversionCPA");
            Assert.IsNotNull(response["@ns.uncategorized"].PartialConversionRate, "Missing PartialConversionRate");
            Assert.IsNotNull(response["@ns.uncategorized"].AllConversionsCredit, "Missing AllConversionsCredit");
            Assert.IsNotNull(response["@ns.uncategorized"].AllPartialConversionCPA, "Missing AllPartialConversionCPA");
            Assert.IsNotNull(response["@ns.uncategorized"].AllPartialConversionRate, "Missing AllPartialConversionRate");
        }
    }
}
